'use client'

import { useEffect, useState, useCallback, useMemo } from 'react'
import { createClient } from '@/lib/supabase/client'
import { useTranslations, useLocale } from 'next-intl'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Trophy, Star, Crown } from 'lucide-react'
import { useRouter } from 'next/navigation'
import { AdminBackButton } from '@/components/admin/admin-back-button'

import { useToast } from '@/hooks/use-toast'
import LeagueManagement from '@/components/admin/league-management'
import GiftRewardsManager from '@/components/admin/gift-rewards-manager'

export default function AdminGamificationPage() {
  const locale = useLocale()
  const router = useRouter()
  const t = useTranslations('admin')
  const supabase = useMemo(() => createClient(), [])

  const [loading, setLoading] = useState(true)
  const [authChecked, setAuthChecked] = useState(false)
  const [saving, setSaving] = useState(false)
  const [pointsPerChf, setPointsPerChf] = useState<number>(1.0)
  const [userLevels, setUserLevels] = useState<Array<{
    id: string;
    level: number;
    name: string;
    minimum_points: number;
    discount_percentage: number;
    points_multiplier: number;
  }>>([])
  const { toast } = useToast()

  const loadGamificationData = useCallback(async () => {
    try {
      console.log('Loading gamification data...')

      // Get user levels
      const { data: userLevelsData, error: levelsError } = await supabase
        .from('user_levels')
        .select('*')
        .order('level', { ascending: true })

      if (levelsError) {
        console.error('Error fetching user levels:', levelsError)
      }



      // Get points per CHF setting
      const { data: settingsData, error: settingsError } = await supabase
        .from('site_settings')
        .select('points_per_chf')
        .maybeSingle()

      if (settingsError) {
        console.error('Error fetching settings:', settingsError)
      }

      setUserLevels(userLevelsData || [])
      setPointsPerChf(settingsData?.points_per_chf || 1.0)
      setLoading(false)
      console.log('Gamification data loaded successfully')
    } catch (error) {
      console.error('Error loading gamification data:', error)
      setLoading(false)
    }
  }, [supabase])

  const handlePointsPerChfSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setSaving(true)

    try {
      const formData = new FormData(e.target as HTMLFormElement)
      const newPointsPerChf = parseFloat(formData.get('points-per-chf') as string) || 1.0

      // Update the existing settings record (there should be only one)
      const { error } = await supabase
        .from('site_settings')
        .update({ points_per_chf: newPointsPerChf })
        .eq('store_name', 'PrimeCaffe') // Use a unique identifier

      if (error) {
        throw error
      }

      setPointsPerChf(newPointsPerChf)
      toast({
        title: t('saveSuccess'),
        description: t('saveSuccessMessage'),
      })
    } catch (error) {
      console.error('Error saving points per CHF:', error)
      toast({
        title: t('saveError'),
        description: t('saveErrorMessage'),
        variant: 'destructive',
      })
    } finally {
      setSaving(false)
    }
  }

  useEffect(() => {
    async function checkAuthAndLoadData() {
      try {
        if (authChecked) return

        console.log('Checking authentication...')
        const { data: { user }, error: authError } = await supabase.auth.getUser()

        if (authError) {
          console.error('Auth error:', authError)
          setAuthChecked(true)
          router.push(`/${locale}/login`)
          return
        }

        if (!user) {
          console.log('No user found, redirecting to login')
          setAuthChecked(true)
          router.push(`/${locale}/login`)
          return
        }

        console.log('User found:', user.email)

        const { data: profile, error: profileError } = await supabase
          .from('users')
          .select('is_admin')
          .eq('id', user.id)
          .single()

        if (profileError) {
          console.error('Profile error:', profileError)
          setAuthChecked(true)
          setLoading(false)
          return
        }

        console.log('Profile loaded:', profile)

        if (!profile?.is_admin) {
          console.log('User is not admin, redirecting')
          setAuthChecked(true)
          router.push(`/${locale}`)
          return
        }

        console.log('User is admin, loading gamification data')
        setAuthChecked(true)
        await loadGamificationData()
      } catch (error) {
        console.error('Error in checkAuthAndLoadData:', error)
        setAuthChecked(true)
        setLoading(false)
      }
    }

    if (!authChecked) {
      checkAuthAndLoadData()
    }
  }, [authChecked, locale, router, supabase, loadGamificationData])

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-muted-foreground">{t('gamificationPage.loading')}</p>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto py-8">
      {/* Back to Dashboard Button */}
      <div className="mb-6">
        <AdminBackButton />
      </div>

      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold">{t('gamificationPage.title')}</h1>
          <p className="text-muted-foreground">
            {t('gamificationPage.subtitle')}
          </p>
        </div>
      </div>

      {/* Points Configuration */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Star className="h-5 w-5" />
            {t('gamificationSettings.title')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handlePointsPerChfSubmit} className="space-y-4">
            <div>
              <Label htmlFor="points-per-chf">{t('gamificationSettings.pointsPerCHF')}</Label>
              <Input
                id="points-per-chf"
                name="points-per-chf"
                type="number"
                step="0.1"
                min="0"
                defaultValue={pointsPerChf}
                placeholder="1.0"
                className="max-w-xs"
              />
              <p className="text-sm text-muted-foreground mt-1">
                {t('gamificationSettings.pointsPerCHFDesc')}
              </p>
            </div>
            <Button type="submit" disabled={saving}>
              {saving ? t('saving') : t('save')}
            </Button>
          </form>
        </CardContent>
      </Card>

      {/* League Management */}
      <LeagueManagement onConfigUpdated={loadGamificationData} />



      {/* Gift Rewards Management */}
      <GiftRewardsManager />

      {/* Gamification Statistics */}
      <div className="grid md:grid-cols-2 gap-4 mt-8">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">{t('gamificationPage.activeLevels')}</p>
                <p className="text-2xl font-bold">{userLevels?.length || 0}</p>
              </div>
              <Trophy className="h-8 w-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">{t('gamificationPage.maxPointsMultiplier')}</p>
                <p className="text-2xl font-bold">
                  {userLevels && userLevels.length > 0
                    ? `${Math.max(...userLevels.map(l => l.points_multiplier))}x`
                    : '1x'
                  }
                </p>
              </div>
              <Crown className="h-8 w-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>
      </div>


    </div>
  )
}
