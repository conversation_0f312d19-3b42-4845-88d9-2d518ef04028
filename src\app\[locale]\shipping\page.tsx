"use client";

'use client';

import { useTranslations } from 'next-intl';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Truck, Clock, MapPin, Package } from 'lucide-react';
import { useEffect, useState } from 'react';
import { createClient } from '@/lib/supabase/client';

interface ShippingRate {
  id: string
  country: string
  cost: number
  free_shipping_threshold: number
  estimated_days: string
  is_active: boolean
}

export default function ShippingPage() {
  const t = useTranslations('shipping');
  const [shippingRates, setShippingRates] = useState<ShippingRate[]>([]);
  const [loading, setLoading] = useState(true);
  const supabase = createClient();

  useEffect(() => {
    const loadShippingRates = async () => {
      try {
        const { data, error } = await supabase
          .from('shipping_rates')
          .select('*')
          .eq('is_active', true)
          .order('country');

        if (error) {
          console.error('Error loading shipping rates:', error);
        } else {
          setShippingRates(data || []);
        }
      } catch (error) {
        console.error('Error loading shipping rates:', error);
      } finally {
        setLoading(false);
      }
    };

    loadShippingRates();
  }, [supabase]);

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-12">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-orange-100 rounded-full mb-6">
            <Truck className="h-8 w-8 text-orange-600" />
          </div>
          <h1 className="text-4xl font-bold mb-4">{t('title')}</h1>
          <p className="text-xl text-muted-foreground">
            {t('subtitle')}
          </p>
        </div>

        <div className="space-y-8">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MapPin className="h-5 w-5" />
                {t('shippingCosts')}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full border-collapse">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left p-3">{t('country')}</th>
                      <th className="text-left p-3">{t('shippingCost')}</th>
                      <th className="text-left p-3">{t('freeShippingFrom')}</th>
                      <th className="text-left p-3">{t('deliveryTime')}</th>
                    </tr>
                  </thead>
                  <tbody>
                    {loading ? (
                      <tr>
                        <td colSpan={4} className="p-3 text-center text-muted-foreground">
                          Caricamento...
                        </td>
                      </tr>
                    ) : shippingRates.length > 0 ? (
                      shippingRates.map((rate) => (
                        <tr key={rate.id} className="border-b">
                          <td className="p-3">{rate.country}</td>
                          <td className="p-3">CHF {rate.cost.toFixed(2)}</td>
                          <td className="p-3">CHF {rate.free_shipping_threshold.toFixed(2)}</td>
                          <td className="p-3">{rate.estimated_days}</td>
                        </tr>
                      ))
                    ) : (
                      <tr>
                        <td colSpan={4} className="p-3 text-center text-muted-foreground">
                          Nessuna tariffa di spedizione configurata
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="h-5 w-5" />
                {t('processingTime')}
              </CardTitle>
            </CardHeader>
            <CardContent className="prose prose-sm max-w-none">
              <p>
                {t('processingDescription')}
              </p>
              <p>
                <strong>{t('processingNote')}</strong>
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Package className="h-5 w-5" />
                {t('packaging')}
              </CardTitle>
            </CardHeader>
            <CardContent className="prose prose-sm max-w-none">
              <p>
                {t('packagingDescription')}
              </p>
              <ul>
                <li>{t('recyclableBoxes')}</li>
                <li>{t('biodegradableFilling')}</li>
                <li>{t('securePadding')}</li>
              </ul>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>{t('tracking')}</CardTitle>
            </CardHeader>
            <CardContent className="prose prose-sm max-w-none">
              <p>
                {t('trackingDescription')}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>{t('deliveryProblems')}</CardTitle>
            </CardHeader>
            <CardContent className="prose prose-sm max-w-none">
              <p>
                {t('deliveryProblemsDescription')}
              </p>
              <p>
                <strong>{t('email')}:</strong> <EMAIL><br />
                <strong>{t('phone')}:</strong> +41 79 342 65 74
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
