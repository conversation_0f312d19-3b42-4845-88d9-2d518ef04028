import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { sendContactFormNotificationEmail } from '@/lib/email'

export async function POST(request: NextRequest) {
  try {
    const formData = await request.json()
    const { firstName, lastName, email, phone, subject, message } = formData

    // Validate required fields
    if (!firstName || !lastName || !email || !subject || !message) {
      return NextResponse.json(
        { error: 'Tutti i campi obbligatori devono essere compilati' },
        { status: 400 }
      )
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { error: 'Formato email non valido' },
        { status: 400 }
      )
    }

    const supabase = await createClient()

    // Store contact form submission in database
    const { error: insertError } = await supabase
      .from('contact_submissions')
      .insert({
        first_name: firstName,
        last_name: lastName,
        email,
        phone: phone || null,
        subject,
        message,
        created_at: new Date().toISOString()
      })

    if (insertError) {
      console.error('Error storing contact submission:', insertError)
      // Don't fail the request if we can't store it, just log it
    }

    // Send email notification to admin
    try {
      await sendContactFormNotificationEmail({
        firstName,
        lastName,
        email,
        phone: phone || '',
        subject,
        message
      })
      console.log('Contact form notification email sent successfully')
    } catch (emailError) {
      console.error('Error sending contact form notification email:', emailError)
      // Don't fail the request if email fails, just log it
    }

    return NextResponse.json({
      success: true,
      message: 'Messaggio inviato con successo! Ti risponderemo al più presto.'
    })

  } catch (error) {
    console.error('Error in contact form:', error)
    return NextResponse.json(
      { error: 'Errore interno del server. Riprova più tardi.' },
      { status: 500 }
    )
  }
}
