import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

// Normalize brand name to prevent case-sensitive duplicates
async function normalizeBrand(brand: string | null, supabase: Awaited<ReturnType<typeof createClient>>): Promise<string | null> {
  if (!brand || typeof brand !== 'string') {
    return null
  }

  // Trim whitespace
  const trimmedBrand = brand.trim()
  if (!trimmedBrand) {
    return null
  }

  // Check if a similar brand already exists (case-insensitive)
  const { data: existingBrands } = await supabase
    .from('products')
    .select('brand')
    .not('brand', 'is', null)

  if (existingBrands) {
    // Find existing brand with same name (case-insensitive)
    const existingBrand = existingBrands.find((p: { brand: string | null }) =>
      p.brand && p.brand.toLowerCase().trim() === trimmedBrand.toLowerCase()
    )

    if (existingBrand) {
      // Use the existing brand's exact casing
      return existingBrand.brand.trim()
    }
  }

  // Return the trimmed brand with original casing if no match found
  return trimmedBrand
}

export async function GET() {
  try {
    const supabase = await createClient()
    
    // Check if user is admin
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json(
        { error: 'Nicht autorisiert' },
        { status: 401 }
      )
    }

    const { data: profile } = await supabase
      .from('users')
      .select('is_admin')
      .eq('id', user.id)
      .single()

    if (!profile?.is_admin) {
      return NextResponse.json(
        { error: 'Nicht autorisiert' },
        { status: 403 }
      )
    }

    // Get all products
    const { data: products, error } = await supabase
      .from('products')
      .select('*')
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Error fetching products:', error)
      return NextResponse.json(
        { error: 'Fehler beim Laden der Produkte' },
        { status: 500 }
      )
    }

    return NextResponse.json({ products })
  } catch (error) {
    console.error('Error in products GET:', error)
    return NextResponse.json(
      { error: 'Interner Serverfehler' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const productData = await request.json()
    
    const supabase = await createClient()
    
    // Check if user is admin
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json(
        { error: 'Nicht autorisiert' },
        { status: 401 }
      )
    }

    const { data: profile } = await supabase
      .from('users')
      .select('is_admin')
      .eq('id', user.id)
      .single()

    if (!profile?.is_admin) {
      return NextResponse.json(
        { error: 'Nicht autorisiert' },
        { status: 403 }
      )
    }

    // Clean up empty string values for enum and UUID fields
    if (productData.coffee_type === '') {
      productData.coffee_type = null
    }
    if (productData.brand === '') {
      productData.brand = null
    }
    if (productData.blend === '') {
      productData.blend = null
    }
    if (productData.template_id === '') {
      productData.template_id = null
    }
    if (productData.title_template === '') {
      productData.title_template = null
    }

    // Convert discount_price of 0 to null to prevent showing "CHF 0.00"
    if (productData.discount_price === 0 || productData.discount_price === '0') {
      productData.discount_price = null
    }

    // Handle category-specific field validation and cleanup
    if (productData.category === 'accessories') {
      // For accessories, clear coffee-specific fields
      productData.coffee_type = null
      productData.blend = null
      productData.pack_quantity = null
      productData.pack_weight_grams = null
      productData.cost_per_espresso = null
      productData.machine_compatibility = null
    } else if (productData.category === 'coffee') {
      // For coffee products, ensure coffee_type is provided
      if (!productData.coffee_type) {
        return NextResponse.json(
          { error: 'Tipo di caffè è richiesto per i prodotti della categoria caffè' },
          { status: 400 }
        )
      }
    }

    // Normalize brand name to prevent case-sensitive duplicates
    productData.brand = await normalizeBrand(productData.brand, supabase)

    // Validate required fields
    if (!productData.title || !productData.price || !productData.category) {
      return NextResponse.json(
        { error: 'Titel, Preis und Kategorie sind erforderlich' },
        { status: 400 }
      )
    }

    // Generate slug from title
    // Generate slug from title
    const slug = productData.title
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)/g, '')

    // Calculate cost_per_espresso automatically
    let cost_per_espresso: number | null = null

    // Use discount price if available, otherwise use regular price
    const effectivePrice = productData.discount_price && productData.discount_price > 0
      ? productData.discount_price
      : productData.price

    if ((productData.coffee_type === 'capsules' || productData.coffee_type === 'pods') && productData.pack_quantity) {
      cost_per_espresso = parseFloat((effectivePrice / productData.pack_quantity).toFixed(4))
    } else if ((productData.coffee_type === 'beans' || productData.coffee_type === 'ground') && productData.pack_weight_grams) {
      const espressoCount = productData.pack_weight_grams / 7.5
      if (espressoCount > 0) {
        cost_per_espresso = parseFloat((effectivePrice / espressoCount).toFixed(4))
      }
    }

    // Create product
    const { data: product, error } = await supabase
      .from('products')
      .insert({
        title: productData.title,
        description: productData.description || '',
        slug,
        category: productData.category,
        coffee_type: productData.coffee_type || null,
        brand: productData.brand || null,
        blend: productData.blend || null,
        machine_compatibility: productData.machine_compatibility || [],
        pack_quantity: productData.pack_quantity || null,
        pack_weight_grams: productData.pack_weight_grams || null,
        price: productData.price,
        discount_price: productData.discount_price || null,
        cost_per_espresso: cost_per_espresso,
        images: productData.images || [],
        inventory_count: productData.inventory_count || 0,
        purchase_cost: productData.purchase_cost || null,
        is_available: productData.is_available ?? true,
        translations: productData.translations || null,
      })
      .select()
      .single()

    if (error) {
      console.error('Error creating product:', error)
      return NextResponse.json(
        { error: 'Fehler beim Erstellen des Produkts' },
        { status: 500 }
      )
    }

    return NextResponse.json({ success: true, product })
  } catch (error) {
    console.error('Error in product create:', error)
    return NextResponse.json(
      { error: 'Interner Serverfehler' },
      { status: 500 }
    )
  }
}
