'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog'
import { Trash2, Loader2, Eye, AlertTriangle } from 'lucide-react'
import { formatCurrency } from '@/lib/utils'

interface TestOrder {
  id: string
  order_number: string | null
  email: string
  status: string
  payment_status: string
  total_amount: number
  created_at: string
  hours_since_creation: number
}

interface CleanupResult {
  success: boolean
  message: string
  deletedCount: number
  testEmails: string[]
  deletedOrders?: TestOrder[]
  testOrders?: TestOrder[]
}

export default function DevTestOrdersCleanup() {
  const [isLoading, setIsLoading] = useState(false)
  const [isChecking, setIsChecking] = useState(false)
  const [result, setResult] = useState<CleanupResult | null>(null)
  const [testOrders, setTestOrders] = useState<TestOrder[]>([])
  const [showPreview, setShowPreview] = useState(false)

  // Check what test orders exist
  const checkTestOrders = async () => {
    setIsChecking(true)
    try {
      const response = await fetch('/api/admin/dev-cleanup-test-orders', {
        method: 'GET',
      })

      const data = await response.json()

      if (data.success) {
        setTestOrders(data.testOrders || [])
        setShowPreview(true)
      } else {
        console.error('Error checking test orders:', data.error)
      }
    } catch (error) {
      console.error('Error checking test orders:', error)
    } finally {
      setIsChecking(false)
    }
  }

  // Delete test orders
  const deleteTestOrders = async () => {
    setIsLoading(true)
    setResult(null)
    
    try {
      const response = await fetch('/api/admin/dev-cleanup-test-orders', {
        method: 'POST',
      })

      const data = await response.json()
      setResult(data)
      setShowPreview(false)
      
      if (data.success) {
        console.log(`✅ Deleted ${data.deletedCount} test orders`)
      } else {
        console.error('❌ Error deleting test orders:', data.error)
      }
    } catch (error) {
      console.error('❌ Error deleting test orders:', error)
      setResult({
        success: false,
        message: 'Network error occurred',
        deletedCount: 0,
        testEmails: []
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <>
      <AlertDialog open={showPreview} onOpenChange={setShowPreview}>
        <AlertDialogTrigger asChild>
          <Button 
            variant="outline" 
            onClick={checkTestOrders}
            disabled={isChecking}
            className="border-orange-200 text-orange-700 hover:bg-orange-50"
          >
            {isChecking ? (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            ) : (
              <Eye className="mr-2 h-4 w-4" />
            )}
            Ordini Test
          </Button>
        </AlertDialogTrigger>
        
        <AlertDialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-orange-500" />
              Elimina Ordini di Test
            </AlertDialogTitle>
            <AlertDialogDescription>
              Questa funzione eliminerà TUTTI gli ordini associati alle email di test:
              <div className="mt-2 space-y-1">
                <Badge variant="secondary"><EMAIL></Badge>
                <Badge variant="secondary"><EMAIL></Badge>
              </div>
            </AlertDialogDescription>
          </AlertDialogHeader>

          <div className="py-4">
            {testOrders.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <Trash2 className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>Nessun ordine di test trovato</p>
              </div>
            ) : (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h4 className="font-medium">Ordini da eliminare ({testOrders.length})</h4>
                </div>
                
                <div className="space-y-2 max-h-60 overflow-y-auto">
                  {testOrders.map((order) => (
                    <div key={order.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div>
                        <div className="font-medium text-sm">{order.email}</div>
                        <div className="text-xs text-muted-foreground">
                          {order.order_number || order.id.slice(0, 8)}... • {order.hours_since_creation}h fa
                        </div>
                        <div className="flex gap-2 mt-1">
                          <Badge variant={order.status === 'pending' ? 'secondary' : 'outline'} className="text-xs">
                            {order.status}
                          </Badge>
                          <Badge variant={order.payment_status === 'pending' ? 'secondary' : 'outline'} className="text-xs">
                            {order.payment_status}
                          </Badge>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-semibold text-sm">{formatCurrency(order.total_amount)}</div>
                        <div className="text-xs text-muted-foreground">
                          {new Date(order.created_at).toLocaleDateString('it-CH')}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          <AlertDialogFooter>
            <AlertDialogCancel>Annulla</AlertDialogCancel>
            {testOrders.length > 0 && (
              <AlertDialogAction
                onClick={deleteTestOrders}
                disabled={isLoading}
                className="bg-red-600 hover:bg-red-700"
              >
                {isLoading ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <Trash2 className="mr-2 h-4 w-4" />
                )}
                Elimina {testOrders.length} ordini
              </AlertDialogAction>
            )}
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Result display */}
      {result && (
        <div className={`mt-4 p-4 rounded-lg ${
          result.success 
            ? 'bg-green-50 border border-green-200' 
            : 'bg-red-50 border border-red-200'
        }`}>
          <div className={`font-medium ${
            result.success ? 'text-green-800' : 'text-red-800'
          }`}>
            {result.message}
          </div>
          {result.success && result.deletedCount > 0 && (
            <div className="text-sm text-green-700 mt-1">
              Eliminati {result.deletedCount} ordini di test
            </div>
          )}
        </div>
      )}
    </>
  )
}
