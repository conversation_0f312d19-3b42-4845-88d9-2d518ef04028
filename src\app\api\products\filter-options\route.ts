import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const category = searchParams.get('category')
    const coffeeType = searchParams.get('coffee_type')
    const brand = searchParams.get('brand')

    const supabase = await createClient()

    // Build query with filters
    let query = supabase
      .from('products')
      .select('category, coffee_type, brand, blend, machine_compatibility')
      .eq('is_available', true)

    // Apply filters to get relevant options
    if (category && category !== 'all') {
      query = query.eq('category', category)
    }
    if (coffeeType && coffeeType !== 'all') {
      query = query.eq('coffee_type', coffeeType)
    }
    if (brand && brand !== 'all') {
      query = query.eq('brand', brand)
    }

    const { data: products, error } = await query

    if (error) {
      console.error('Error fetching products for filter options:', error)
      return NextResponse.json(
        { error: 'Failed to fetch filter options' },
        { status: 500 }
      )
    }

    // Build filter options from all products
    const categories = [...new Set(products.map(p => p.category).filter(Boolean))] as string[]
    
    const coffeeTypes = [...new Set(products.map(p => p.coffee_type).filter(Boolean))] as string[]
    
    const brands = [...new Set(products.map(p => p.brand).filter(Boolean))] as string[]
    
    const blends = [...new Set(products.map(p => p.blend).filter(Boolean))] as string[]
    
    const compatibilities = [...new Set(
      products
        .filter(p => p.machine_compatibility && p.machine_compatibility.length > 0)
        .flatMap(p => p.machine_compatibility || [])
        .filter(Boolean)
    )] as string[]

    return NextResponse.json({
      categories: categories.sort(),
      coffeeTypes: coffeeTypes.sort(),
      brands: brands.sort(),
      blends: blends.sort(),
      compatibilities: compatibilities.sort()
    })

  } catch (error) {
    console.error('Error in filter options API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
