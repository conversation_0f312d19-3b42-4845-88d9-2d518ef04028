-- Migration: Simplified Level Generation System
-- Date: 2025-07-08
-- Description: Create a function to automatically generate levels based on league configuration

-- Create function to generate levels automatically
CREATE OR REPLACE FUNCTION generate_levels_from_config()
RETURNS void
LANGUAGE plpgsql
AS $$
DECLARE
    config_row league_config%ROWTYPE;
    league_names TEXT[] := ARRAY['Bronze', 'Silver', 'Gold', 'Platinum', 'Diamond'];
    level_numbers TEXT[] := ARRAY['I', 'II', 'III', 'IV', 'V', 'VI', 'VII', 'VIII', 'IX', 'X', 'XI', 'XII', 'XIII', 'XIV', 'XV', 'XVI', 'XVII', 'XVIII', 'XIX', 'XX'];
    league_points INTEGER[];
    league_discounts DECIMAL[];
    start_points INTEGER;
    end_points INTEGER;
    points_per_level INTEGER;
    global_level INTEGER;
    level_points INTEGER;
    league_idx INTEGER;
    level_idx INTEGER;
BEGIN
    -- Get active league configuration
    SELECT * INTO config_row 
    FROM league_config 
    WHERE is_active = true 
    LIMIT 1;
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'No active league configuration found';
    END IF;
    
    -- Prepare league data
    league_points := ARRAY[
        config_row.league_1_points,
        config_row.league_2_points,
        config_row.league_3_points,
        config_row.league_4_points,
        config_row.league_5_points
    ];
    
    league_discounts := ARRAY[
        config_row.league_1_discount,
        config_row.league_2_discount,
        config_row.league_3_discount,
        config_row.league_4_discount,
        config_row.league_5_discount
    ];
    
    -- Clear existing levels
    DELETE FROM user_levels WHERE level > 0;
    
    -- Generate levels for each league
    FOR league_idx IN 1..5 LOOP
        start_points := league_points[league_idx];
        
        -- Calculate end points (next league start or +5000 for last league)
        IF league_idx < 5 THEN
            end_points := league_points[league_idx + 1];
        ELSE
            end_points := start_points + 5000;
        END IF;
        
        -- Calculate points per level in this league
        points_per_level := (end_points - start_points) / config_row.levels_per_league;
        
        -- Generate levels for this league
        FOR level_idx IN 1..config_row.levels_per_league LOOP
            global_level := (league_idx - 1) * config_row.levels_per_league + level_idx;
            level_points := start_points + ((level_idx - 1) * points_per_level);
            
            INSERT INTO user_levels (
                level,
                name,
                minimum_points,
                discount_percentage,
                points_multiplier
            ) VALUES (
                global_level,
                league_names[league_idx] || ' ' || level_numbers[level_idx],
                level_points,
                league_discounts[league_idx],
                1.0
            );
        END LOOP;
    END LOOP;
    
    RAISE NOTICE 'Generated % levels successfully', config_row.levels_per_league * 5;
END;
$$;

-- Test the function with current configuration
SELECT generate_levels_from_config();

-- Add comment
COMMENT ON FUNCTION generate_levels_from_config() IS 'Automatically generates user levels based on league configuration. Clears existing levels and creates new ones with proper point distribution.';

-- Create function to update all user levels after configuration changes
CREATE OR REPLACE FUNCTION update_all_user_levels()
RETURNS TABLE(users_updated INTEGER, total_users INTEGER)
LANGUAGE plpgsql
AS $$
DECLARE
    user_record RECORD;
    correct_level_record user_levels%ROWTYPE;
    users_updated_count INTEGER := 0;
    total_users_count INTEGER := 0;
BEGIN
    -- Count total users
    SELECT COUNT(*) INTO total_users_count FROM users WHERE total_points IS NOT NULL;

    -- Update each user's level based on their current points
    FOR user_record IN
        SELECT id, total_points, current_level
        FROM users
        WHERE total_points IS NOT NULL
    LOOP
        total_users_count := total_users_count + 1;

        -- Find the correct level for this user's points
        SELECT * INTO correct_level_record
        FROM user_levels
        WHERE minimum_points <= COALESCE(user_record.total_points, 0)
        ORDER BY level DESC
        LIMIT 1;

        -- Update user if their current level is incorrect
        IF correct_level_record.level IS NOT NULL AND
           (user_record.current_level IS NULL OR user_record.current_level != correct_level_record.level) THEN

            UPDATE users
            SET current_level = correct_level_record.level
            WHERE id = user_record.id;

            users_updated_count := users_updated_count + 1;

            RAISE NOTICE 'Updated user % from level % to level %',
                user_record.id,
                COALESCE(user_record.current_level, 0),
                correct_level_record.level;
        END IF;
    END LOOP;

    RETURN QUERY SELECT users_updated_count, total_users_count;
END;
$$;

-- Add comment
COMMENT ON FUNCTION update_all_user_levels() IS 'Updates all users to their correct level based on current points and level configuration. Returns count of updated users and total users processed.';
