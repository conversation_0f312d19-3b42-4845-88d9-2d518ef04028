import { useState, useEffect } from 'react'

interface FilterOptions {
  categories: string[]
  coffeeTypes: string[]
  brands: string[]
  blends: string[]
  compatibilities: string[]
}

interface UseFilterOptionsReturn {
  filterOptions: FilterOptions
  loading: boolean
  error: string | null
}

export function useFilterOptions(): UseFilterOptionsReturn {
  const [filterOptions, setFilterOptions] = useState<FilterOptions>({
    categories: [],
    coffeeTypes: [],
    brands: [],
    blends: [],
    compatibilities: []
  })
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchFilterOptions = async () => {
      try {
        setLoading(true)
        setError(null)
        
        const response = await fetch('/api/products/filter-options')
        
        if (!response.ok) {
          throw new Error('Failed to fetch filter options')
        }
        
        const data = await response.json()
        setFilterOptions(data)
        
      } catch (err) {
        console.error('Error fetching filter options:', err)
        setError(err instanceof Error ? err.message : 'Unknown error')
      } finally {
        setLoading(false)
      }
    }

    fetchFilterOptions()
  }, [])

  return {
    filterOptions,
    loading,
    error
  }
}
