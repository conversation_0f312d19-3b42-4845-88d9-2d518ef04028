/**
 * Test suite for gamification auto-update functionality
 * Tests that when admin changes league configuration, all levels and users are updated correctly
 */

import { describe, it, expect, beforeEach, afterEach } from '@jest/globals'

// Mock the entire gamification module to avoid Supabase import issues
const mockGenerateLevelsFromConfig = jest.fn()
const mockUpdateAllUserLevels = jest.fn()
const mockGetLeagueConfig = jest.fn()

jest.mock('@/lib/gamification', () => ({
  generateLevelsFromConfig: mockGenerateLevelsFromConfig,
  updateAllUserLevels: mockUpdateAllUserLevels,
  getLeagueConfig: mockGetLeagueConfig
}))

describe('Gamification Auto-Update System', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  afterEach(() => {
    jest.clearAllMocks()
  })

  describe('Level Generation', () => {
    it('should generate levels automatically when configuration changes', async () => {
      // Mock successful level generation
      mockGenerateLevelsFromConfig.mockResolvedValue(undefined)

      await mockGenerateLevelsFromConfig()

      expect(mockGenerateLevelsFromConfig).toHaveBeenCalled()
    })

    it('should handle errors during level generation', async () => {
      // Mock error during level generation
      const mockError = new Error('Database error')
      mockGenerateLevelsFromConfig.mockRejectedValue(mockError)

      await expect(mockGenerateLevelsFromConfig()).rejects.toThrow('Database error')
    })
  })

  describe('User Level Updates', () => {
    it('should update all users to correct levels', async () => {
      // Mock successful user update
      mockUpdateAllUserLevels.mockResolvedValue({
        usersUpdated: 5,
        totalUsers: 10
      })

      const result = await mockUpdateAllUserLevels()

      expect(mockUpdateAllUserLevels).toHaveBeenCalled()
      expect(result).toEqual({
        usersUpdated: 5,
        totalUsers: 10
      })
    })

    it('should handle case when no users need updating', async () => {
      // Mock no users updated
      mockUpdateAllUserLevels.mockResolvedValue({
        usersUpdated: 0,
        totalUsers: 10
      })

      const result = await mockUpdateAllUserLevels()

      expect(result).toEqual({
        usersUpdated: 0,
        totalUsers: 10
      })
    })

    it('should handle errors during user updates', async () => {
      // Mock error during user update
      const mockError = new Error('Update failed')
      mockUpdateAllUserLevels.mockRejectedValue(mockError)

      await expect(mockUpdateAllUserLevels()).rejects.toThrow('Update failed')
    })
  })

  describe('League Configuration', () => {
    it('should fetch active league configuration', async () => {
      const mockConfig = {
        id: 'test-id',
        levels_per_league: 10,
        league_1_points: 0,
        league_2_points: 1000,
        league_3_points: 3000,
        league_4_points: 6000,
        league_5_points: 10000,
        is_active: true
      }

      mockGetLeagueConfig.mockResolvedValue(mockConfig)

      const config = await mockGetLeagueConfig()

      expect(config).toEqual(mockConfig)
      expect(mockGetLeagueConfig).toHaveBeenCalled()
    })
  })

  describe('Integration Scenarios', () => {
    it('should handle complete configuration update workflow', async () => {
      // Mock successful level generation and user updates
      mockGenerateLevelsFromConfig.mockResolvedValue(undefined)
      mockUpdateAllUserLevels.mockResolvedValue({
        usersUpdated: 3,
        totalUsers: 5
      })

      // Simulate the workflow that happens when admin saves configuration
      await mockGenerateLevelsFromConfig()
      const updateResult = await mockUpdateAllUserLevels()

      expect(mockGenerateLevelsFromConfig).toHaveBeenCalled()
      expect(mockUpdateAllUserLevels).toHaveBeenCalled()
      expect(updateResult.usersUpdated).toBe(3)
      expect(updateResult.totalUsers).toBe(5)
    })
  })

  describe('Point Distribution Logic', () => {
    it('should correctly calculate points per level within leagues', () => {
      // Test the mathematical logic used in the SQL function
      const levelsPerLeague = 10
      const league1Points = 0
      const league2Points = 1000
      
      // Points per level = (end_points - start_points) / levels_per_league
      const pointsPerLevel = (league2Points - league1Points) / levelsPerLeague
      expect(pointsPerLevel).toBe(100)
      
      // Level 1 should start at league1Points
      const level1Points = league1Points + ((1 - 1) * pointsPerLevel)
      expect(level1Points).toBe(0)
      
      // Level 5 should be at league1Points + 4 * pointsPerLevel
      const level5Points = league1Points + ((5 - 1) * pointsPerLevel)
      expect(level5Points).toBe(400)
      
      // Level 10 should be at league1Points + 9 * pointsPerLevel
      const level10Points = league1Points + ((10 - 1) * pointsPerLevel)
      expect(level10Points).toBe(900)
    })

    it('should handle different league configurations', () => {
      // Test with different league point thresholds
      const levelsPerLeague = 10
      const league2Points = 2000
      const league3Points = 5000
      
      const pointsPerLevel = (league3Points - league2Points) / levelsPerLeague
      expect(pointsPerLevel).toBe(300)
      
      // First level of league 2 (level 11) should start at league2Points
      const level11Points = league2Points + ((1 - 1) * pointsPerLevel)
      expect(level11Points).toBe(2000)
      
      // Last level of league 2 (level 20) should be close to league3Points
      const level20Points = league2Points + ((10 - 1) * pointsPerLevel)
      expect(level20Points).toBe(4700)
    })
  })
})
