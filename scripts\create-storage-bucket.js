#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to create the product-images storage bucket in Supabase
 * This script uses the Supabase client to create the bucket programmatically
 */

const { createClient } = require('@supabase/supabase-js')
const fs = require('fs')
const path = require('path')

// Read environment variables from .env.local
let supabaseUrl, supabaseServiceKey

try {
  const envPath = path.join(process.cwd(), '.env.local')
  const envContent = fs.readFileSync(envPath, 'utf8')

  envContent.split('\n').forEach(line => {
    const [key, value] = line.split('=')
    if (key === 'NEXT_PUBLIC_SUPABASE_URL') {
      supabaseUrl = value
    } else if (key === 'SUPABASE_SERVICE_ROLE_KEY') {
      supabaseServiceKey = value
    }
  })
} catch (error) {
  console.error('❌ Error reading .env.local file:', error.message)
}

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables:')
  console.error('   - NEXT_PUBLIC_SUPABASE_URL')
  console.error('   - SUPABASE_SERVICE_ROLE_KEY')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

const BUCKET_NAME = 'product-images'

async function createStorageBucket() {
  try {
    console.log('🔍 Checking if bucket exists...')
    
    // Check if bucket already exists
    const { data: buckets, error: listError } = await supabase.storage.listBuckets()
    
    if (listError) {
      console.error('❌ Error listing buckets:', listError.message)
      process.exit(1)
    }
    
    const bucketExists = buckets?.some(bucket => bucket.name === BUCKET_NAME)
    
    if (bucketExists) {
      console.log(`✅ Bucket "${BUCKET_NAME}" already exists!`)
      return
    }
    
    console.log(`📦 Creating bucket "${BUCKET_NAME}"...`)
    
    // Create the bucket
    const { error: createError } = await supabase.storage.createBucket(BUCKET_NAME, {
      public: true,
      allowedMimeTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/avif'],
      fileSizeLimit: 5242880 // 5MB
    })
    
    if (createError) {
      console.error('❌ Error creating bucket:', createError.message)
      console.error('   Details:', createError)
      
      if (createError.message?.includes('permission') || createError.message?.includes('unauthorized')) {
        console.log('\n💡 Manual steps required:')
        console.log('   1. Go to https://supabase.com/dashboard/project/kbpbjzdlravmjntzvntv/storage/buckets')
        console.log('   2. Click "New bucket"')
        console.log('   3. Name: product-images')
        console.log('   4. Make it public: ✅')
        console.log('   5. File size limit: 5MB')
        console.log('   6. Allowed file types: image/jpeg, image/png, image/webp, image/avif')
      }
      
      process.exit(1)
    }
    
    console.log(`✅ Bucket "${BUCKET_NAME}" created successfully!`)
    
    // Verify the bucket was created
    const { data: updatedBuckets } = await supabase.storage.listBuckets()
    const newBucket = updatedBuckets?.find(bucket => bucket.name === BUCKET_NAME)
    
    if (newBucket) {
      console.log('📋 Bucket details:')
      console.log(`   - Name: ${newBucket.name}`)
      console.log(`   - Public: ${newBucket.public}`)
      console.log(`   - Created: ${newBucket.created_at}`)
    }
    
  } catch (error) {
    console.error('❌ Unexpected error:', error.message)
    process.exit(1)
  }
}

// Run the script
createStorageBucket()
