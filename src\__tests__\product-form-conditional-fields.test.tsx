import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { useTranslations } from 'next-intl'
import ProductForm from '@/components/admin/product-form'

// Mock next-intl
jest.mock('next-intl', () => ({
  useTranslations: jest.fn()
}))

// Mock the toast hook
jest.mock('@/hooks/use-toast', () => ({
  useToast: () => ({
    toast: jest.fn()
  })
}))

// Mock router
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    refresh: jest.fn()
  })
}))

// Mock fetch
global.fetch = jest.fn()

const mockT = (key: string) => {
  const translations: Record<string, string> = {
    'productForm.titleRequired': 'Titolo *',
    'productForm.description': 'Descrizione',
    'productForm.categoryRequired': 'Categoria *',
    'productForm.categories.coffee': 'Caffè',
    'productForm.categories.accessories': 'Accessori',
    'productForm.coffeeType': 'Tipo di caffè',
    'productForm.brand': 'Marca',
    'productForm.blend': 'Miscela',
    'productForm.packQuantity': 'Quantità per confezione',
    'productForm.packWeight': 'Peso confezione (g)',
    'productForm.costPerEspresso': 'Costo per espresso (CHF)',
    'productForm.priceRequired': 'Prezzo *',
    'productForm.discountPrice': 'Prezzo scontato',
    'productForm.inventory': 'Inventario',
    'productForm.purchaseCost': 'Costo di acquisto',
    'productForm.available': 'Disponibile',
    'productForm.save': 'Salva prodotto',
    'productForm.update': 'Aggiorna prodotto'
  }
  return translations[key] || key
}

describe('Product Form Conditional Fields', () => {
  beforeEach(() => {
    (useTranslations as jest.Mock).mockReturnValue(mockT)
    ;(global.fetch as jest.Mock).mockClear()
  })

  it('should show coffee-specific fields when category is coffee', () => {
    render(<ProductForm locale="it" />)
    
    // Select coffee category
    const categorySelect = screen.getByDisplayValue('Caffè')
    expect(categorySelect).toBeInTheDocument()
    
    // Coffee-specific fields should be visible
    expect(screen.getByLabelText('Miscela')).toBeInTheDocument()
    expect(screen.getByLabelText('Quantità per confezione')).toBeInTheDocument()
    expect(screen.getByLabelText('Peso confezione (g)')).toBeInTheDocument()
    expect(screen.getByLabelText('Costo per espresso (CHF)')).toBeInTheDocument()
  })

  it('should hide coffee-specific fields when category is accessories', async () => {
    render(<ProductForm locale="it" />)
    
    // Change category to accessories
    const categorySelect = screen.getByRole('combobox')
    fireEvent.click(categorySelect)
    
    const accessoriesOption = screen.getByText('Accessori')
    fireEvent.click(accessoriesOption)
    
    await waitFor(() => {
      // Coffee-specific fields should not be visible
      expect(screen.queryByLabelText('Miscela')).not.toBeInTheDocument()
      expect(screen.queryByLabelText('Quantità per confezione')).not.toBeInTheDocument()
      expect(screen.queryByLabelText('Peso confezione (g)')).not.toBeInTheDocument()
      expect(screen.queryByLabelText('Costo per espresso (CHF)')).not.toBeInTheDocument()
    })
  })

  it('should reset coffee-specific fields when switching from coffee to accessories', async () => {
    const mockFetch = global.fetch as jest.Mock
    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({ success: true })
    })

    render(<ProductForm locale="it" />)
    
    // Fill in some coffee-specific fields
    const blendInput = screen.getByLabelText('Miscela')
    const quantityInput = screen.getByLabelText('Quantità per confezione')
    
    fireEvent.change(blendInput, { target: { value: 'Arabica' } })
    fireEvent.change(quantityInput, { target: { value: '10' } })
    
    expect(blendInput).toHaveValue('Arabica')
    expect(quantityInput).toHaveValue(10)
    
    // Change category to accessories
    const categorySelect = screen.getByRole('combobox')
    fireEvent.click(categorySelect)
    
    const accessoriesOption = screen.getByText('Accessori')
    fireEvent.click(accessoriesOption)
    
    // Change back to coffee to verify fields were reset
    fireEvent.click(categorySelect)
    const coffeeOption = screen.getByText('Caffè')
    fireEvent.click(coffeeOption)
    
    await waitFor(() => {
      const newBlendInput = screen.getByLabelText('Miscela')
      const newQuantityInput = screen.getByLabelText('Quantità per confezione')
      
      expect(newBlendInput).toHaveValue('')
      expect(newQuantityInput).toHaveValue(0)
    })
  })

  it('should maintain non-coffee fields when switching categories', async () => {
    render(<ProductForm locale="it" />)
    
    // Fill in general fields
    const titleInput = screen.getByLabelText('Titolo *')
    const priceInput = screen.getByLabelText('Prezzo *')
    
    fireEvent.change(titleInput, { target: { value: 'Test Product' } })
    fireEvent.change(priceInput, { target: { value: '29.90' } })
    
    // Change category to accessories
    const categorySelect = screen.getByRole('combobox')
    fireEvent.click(categorySelect)
    
    const accessoriesOption = screen.getByText('Accessori')
    fireEvent.click(accessoriesOption)
    
    await waitFor(() => {
      // General fields should maintain their values
      expect(titleInput).toHaveValue('Test Product')
      expect(priceInput).toHaveValue(29.90)
    })
  })
})
