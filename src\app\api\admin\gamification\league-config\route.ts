import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { generateLevelsFromConfig, updateAllUserLevels } from '@/lib/gamification'

export interface LeagueConfigRequest {
  levels_per_league: number
  league_1_points: number
  league_2_points: number
  league_3_points: number
  league_4_points: number
  league_5_points: number
  league_1_discount: number
  league_2_discount: number
  league_3_discount: number
  league_4_discount: number
  league_5_discount: number
  global_multiplier: number
  multiplier_description: string
}

// GET - Get current league configuration
export async function GET() {
  try {
    const supabase = await createClient()

    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { data: profile } = await supabase
      .from('users')
      .select('is_admin')
      .eq('id', user.id)
      .single()

    if (!profile?.is_admin) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    // Get league configuration
    const { data: config, error } = await supabase
      .from('league_config')
      .select('*')
      .eq('is_active', true)
      .single()

    if (error && error.code !== 'PGRST116') {
      console.error('Error fetching league config:', error)
      return NextResponse.json({ error: 'Error fetching configuration' }, { status: 500 })
    }

    return NextResponse.json({ success: true, config })
  } catch (error) {
    console.error('Error in league config GET:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// POST - Update league configuration
export async function POST(request: NextRequest) {
  try {
    const data: LeagueConfigRequest = await request.json()
    const supabase = await createClient()

    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { data: profile } = await supabase
      .from('users')
      .select('is_admin')
      .eq('id', user.id)
      .single()

    if (!profile?.is_admin) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    // Validation
    if (data.global_multiplier < 0.1 || data.global_multiplier > 10) {
      return NextResponse.json({ error: 'Global multiplier must be between 0.1 and 10' }, { status: 400 })
    }

    if (data.levels_per_league < 5 || data.levels_per_league > 20) {
      return NextResponse.json({ error: 'Levels per league must be between 5 and 20' }, { status: 400 })
    }

    // Validate league points are in ascending order
    const leaguePoints = [
      data.league_1_points,
      data.league_2_points,
      data.league_3_points,
      data.league_4_points,
      data.league_5_points
    ]

    for (let i = 1; i < leaguePoints.length; i++) {
      if (leaguePoints[i] <= leaguePoints[i - 1]) {
        return NextResponse.json({ error: 'League points must be in ascending order' }, { status: 400 })
      }
    }

    const discounts = [
      data.league_1_discount,
      data.league_2_discount,
      data.league_3_discount,
      data.league_4_discount,
      data.league_5_discount
    ]

    if (discounts.some(d => d < 0 || d > 100)) {
      return NextResponse.json({ error: 'Discount percentages must be between 0 and 100' }, { status: 400 })
    }

    // Check if configuration exists
    const { data: existingConfig } = await supabase
      .from('league_config')
      .select('id')
      .eq('is_active', true)
      .single()

    if (existingConfig) {
      // Update existing configuration
      const { error } = await supabase
        .from('league_config')
        .update({
          levels_per_league: data.levels_per_league,
          league_1_points: data.league_1_points,
          league_2_points: data.league_2_points,
          league_3_points: data.league_3_points,
          league_4_points: data.league_4_points,
          league_5_points: data.league_5_points,
          league_1_discount: data.league_1_discount,
          league_2_discount: data.league_2_discount,
          league_3_discount: data.league_3_discount,
          league_4_discount: data.league_4_discount,
          league_5_discount: data.league_5_discount,
          global_multiplier: data.global_multiplier,
          multiplier_description: data.multiplier_description,
          updated_at: new Date().toISOString()
        })
        .eq('id', existingConfig.id)

      if (error) {
        console.error('Error updating league config:', error)
        return NextResponse.json({ error: 'Error updating configuration' }, { status: 500 })
      }
    } else {
      // Create new configuration
      const { error } = await supabase
        .from('league_config')
        .insert({
          levels_per_league: data.levels_per_league,
          league_1_points: data.league_1_points,
          league_2_points: data.league_2_points,
          league_3_points: data.league_3_points,
          league_4_points: data.league_4_points,
          league_5_points: data.league_5_points,
          league_1_discount: data.league_1_discount,
          league_2_discount: data.league_2_discount,
          league_3_discount: data.league_3_discount,
          league_4_discount: data.league_4_discount,
          league_5_discount: data.league_5_discount,
          global_multiplier: data.global_multiplier,
          multiplier_description: data.multiplier_description,
          is_active: true
        })

      if (error) {
        console.error('Error creating league config:', error)
        return NextResponse.json({ error: 'Error creating configuration' }, { status: 500 })
      }
    }

    // Regenerate levels automatically based on new configuration
    try {
      await generateLevelsFromConfig()
      console.log('✅ Levels regenerated successfully')

      // Update all users to correct levels based on new configuration
      const updateResult = await updateAllUserLevels()
      console.log(`✅ User levels updated: ${updateResult.usersUpdated}/${updateResult.totalUsers} users`)
    } catch (error) {
      console.error('Error regenerating levels:', error)
      // Don't fail the request if level generation fails
    }

    return NextResponse.json({
      success: true,
      message: 'League configuration updated, levels regenerated, and all user levels updated successfully'
    })
  } catch (error) {
    console.error('Error in league config POST:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
