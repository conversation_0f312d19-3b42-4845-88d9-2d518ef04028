import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { createClient as createAdminClient } from '@supabase/supabase-js'

// Create admin client for bypassing RLS
const supabaseAdmin = createAdminClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const orderNumber = searchParams.get('order_number')
  const orderId = searchParams.get('id')

  if (!orderNumber && !orderId) {
    return NextResponse.json(
      { error: 'Either order_number or id parameter is required' },
      { status: 400 }
    )
  }

  console.log(`🔍 API: Looking up order with ${orderNumber ? `order_number: ${orderNumber}` : `id: ${orderId}`}`)

  try {
    const supabase = await createClient()
    const { data: { user } } = await supabase.auth.getUser()
    console.log(`🔍 API: Current user:`, user ? { id: user.id, email: user.email } : 'No user')

    // Build query based on what parameter was provided
    let query = supabaseAdmin
      .from('orders')
      .select(`
        *,
        order_items (
          *,
          products (
            id,
            title,
            price,
            discount_price
          )
        )
      `)

    if (orderNumber) {
      query = query.eq('order_number', orderNumber)
    } else {
      query = query.eq('id', orderId)
    }

    const { data: order, error } = await query.single()

    if (error) {
      console.error('🔍 API: Error fetching order:', error)
      return NextResponse.json(
        { error: 'Order not found' },
        { status: 404 }
      )
    }

    console.log(`🔍 API: Order found:`, {
      id: order.id,
      order_number: order.order_number,
      user_id: order.user_id,
      email: order.email,
      status: order.status,
      payment_status: order.payment_status
    })

    // Check if user has access to this order
    // Admins can access all orders
    // For orders with user_id: only the owner can access
    // For guest orders (user_id is null): anyone can access (they need the order ID/number)
    if (order.user_id && user?.id !== order.user_id) {
      // Check if user is admin using supabaseAdmin
      if (!user) {
        console.log(`🔍 API: Access denied - No user authenticated`)
        return NextResponse.json(
          { error: 'Unauthorized' },
          { status: 403 }
        )
      }

      const { data: profile } = await supabaseAdmin
        .from('users')
        .select('is_admin')
        .eq('id', user.id)
        .single()

      if (!profile?.is_admin) {
        console.log(`🔍 API: Access denied - Order belongs to user ${order.user_id}, current user is ${user?.id} (not admin)`)
        return NextResponse.json(
          { error: 'Unauthorized' },
          { status: 403 }
        )
      } else {
        console.log(`🔍 API: Admin access granted for order ${order.id}`)
      }
    }

    console.log(`🔍 API: Access granted - returning order`)
    return NextResponse.json(order)

  } catch (error) {
    console.error('Error in order lookup API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
