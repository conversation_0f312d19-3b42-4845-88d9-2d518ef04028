import { useState, useEffect, useCallback, useRef } from 'react'

interface Product {
  id: string
  title: string
  description: string
  category: string
  coffee_type: string | null
  brand: string | null
  blend: string | null
  machine_compatibility: string[] | null
  pack_quantity: number | null
  pack_weight_grams: number | null
  price: number
  discount_price: number | null
  cost_per_espresso: number | null
  images: string[]
  is_available: boolean
}

interface PaginationInfo {
  page: number
  limit: number
  total: number
  totalPages: number
  hasMore: boolean
}

interface UseInfiniteScrollProps {
  initialProducts?: Product[]
  filters: {
    category?: string
    coffee_type?: string
    brand?: string
    blend?: string
    compatibility?: string
    coffee_only?: boolean
  }
  limit?: number
  locale?: string
}

interface UseInfiniteScrollReturn {
  products: Product[]
  loading: boolean
  hasMore: boolean
  loadMore: () => void
  reset: () => void
  pagination: PaginationInfo | null
}

export function useInfiniteScroll({
  initialProducts = [],
  filters,
  limit = 20,
  locale = 'de'
}: UseInfiniteScrollProps): UseInfiniteScrollReturn {
  const [products, setProducts] = useState<Product[]>(initialProducts)
  const [loading, setLoading] = useState(false)
  const [pagination, setPagination] = useState<PaginationInfo | null>(null)
  const [currentPage, setCurrentPage] = useState(1)
  const abortControllerRef = useRef<AbortController | null>(null)

  const fetchProducts = useCallback(async (page: number, reset = false) => {
    // Cancel previous request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
    }

    // Create new abort controller
    abortControllerRef.current = new AbortController()

    setLoading(true)

    try {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
        locale: locale,
        ...(filters.category && filters.category !== 'all' && { category: filters.category }),
        ...(filters.coffee_type && filters.coffee_type !== 'all' && { coffee_type: filters.coffee_type }),
        ...(filters.brand && filters.brand !== 'all' && { brand: filters.brand }),
        ...(filters.blend && filters.blend !== 'all' && { blend: filters.blend }),
        ...(filters.compatibility && filters.compatibility !== 'all' && { compatibility: filters.compatibility }),
        ...(filters.coffee_only && { coffee_only: 'true' })
      })

      const response = await fetch(`/api/products?${params}`, {
        signal: abortControllerRef.current.signal
      })

      if (!response.ok) {
        throw new Error('Failed to fetch products')
      }

      const data = await response.json()

      if (reset) {
        setProducts(data.products)
      } else {
        setProducts(prev => [...prev, ...data.products])
      }

      setPagination(data.pagination)
      setCurrentPage(page)

    } catch (error) {
      if (error instanceof Error && error.name !== 'AbortError') {
        console.error('Error fetching products:', error)
      }
    } finally {
      setLoading(false)
    }
  }, [filters, limit, locale])

  const loadMore = useCallback(() => {
    if (!loading && pagination?.hasMore) {
      fetchProducts(currentPage + 1, false)
    }
  }, [loading, pagination?.hasMore, currentPage, fetchProducts])

  const reset = useCallback(() => {
    setProducts([])
    setPagination(null)
    setCurrentPage(1)
    fetchProducts(1, true)
  }, [fetchProducts])

  // Reset when filters change
  useEffect(() => {
    setPagination(null)
    setCurrentPage(1)
    fetchProducts(1, true)
  }, [filters.category, filters.coffee_type, filters.brand, filters.blend, filters.compatibility, filters.coffee_only, fetchProducts])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }
    }
  }, [])

  return {
    products,
    loading,
    hasMore: pagination?.hasMore || false,
    loadMore,
    reset,
    pagination
  }
}
