-- Migration: Fix RLS policies for gamification settings
-- Date: 2025-07-14
-- Description: Add RLS policies for site_settings and league_config tables to allow admin access

-- Enable RLS on site_settings and league_config if not already enabled
ALTER TABLE site_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE league_config ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist (to avoid conflicts)
DROP POLICY IF EXISTS "Ad<PERSON> can view site settings" ON site_settings;
DROP POLICY IF EXISTS "Ad<PERSON> can manage site settings" ON site_settings;
DROP POLICY IF EXISTS "Ad<PERSON> can view league config" ON league_config;
DROP POLICY IF EXISTS "Ad<PERSON> can manage league config" ON league_config;

-- Site settings policies
CREATE POLICY "Ad<PERSON> can view site settings" ON site_settings
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() AND is_admin = true
        )
    );

CREATE POLICY "Admins can manage site settings" ON site_settings
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() AND is_admin = true
        )
    );

-- League config policies
CREATE POLICY "Admins can view league config" ON league_config
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() AND is_admin = true
        )
    );

CREATE POLICY "Admins can manage league config" ON league_config
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() AND is_admin = true
        )
    );

-- Add comments to document the policies
COMMENT ON POLICY "Admins can view site settings" ON site_settings IS 'Allow admin users to view site settings';
COMMENT ON POLICY "Admins can manage site settings" ON site_settings IS 'Allow admin users to create, update, and delete site settings';
COMMENT ON POLICY "Admins can view league config" ON league_config IS 'Allow admin users to view league configuration';
COMMENT ON POLICY "Admins can manage league config" ON league_config IS 'Allow admin users to create, update, and delete league configuration';
