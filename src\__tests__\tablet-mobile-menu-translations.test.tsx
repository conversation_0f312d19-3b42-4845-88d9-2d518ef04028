/**
 * @jest-environment jsdom
 */

import { describe, it, expect } from '@jest/globals'

// Mock messages for testing
const mockMessages = {
  it: {
    navigation: {
      tabletShop: 'Shop',
      tabletBuilder: 'Builder',
      tabletBox: 'Box',
      tabletGifts: '<PERSON>ali',
      tabletAbout: 'Chi siamo'
    },
    account: {
      mobileTabProfile: 'Profilo',
      mobileTabPersonalData: 'Dati',
      mobileTabOrders: 'Ordini',
      mobileTabGifts: '<PERSON><PERSON>',
      mobileTabAddresses: 'Indirizzi',
      mobileTabSecurity: 'Sicurezza'
    }
  },
  de: {
    navigation: {
      tabletShop: 'Shop',
      tabletBuilder: 'Builder',
      tabletBox: 'Bundles',
      tabletGifts: 'G<PERSON>chenke',
      tabletAbout: 'Über uns'
    },
    account: {
      mobileTabProfile: 'Profil',
      mobileTabPersonalData: 'Daten',
      mobileTabOrders: 'Bestellungen',
      mobileTabGifts: 'Geschenke',
      mobileTabAddresses: 'Adressen',
      mobileTabSecurity: 'Sicherheit'
    }
  },
  fr: {
    navigation: {
      tabletShop: 'Boutique',
      tabletBuilder: 'Builder',
      tabletBox: 'Coffrets',
      tabletGifts: 'Cadeaux',
      tabletAbout: 'À propos'
    },
    account: {
      mobileTabProfile: 'Profil',
      mobileTabPersonalData: 'Données',
      mobileTabOrders: 'Commandes',
      mobileTabGifts: 'Cadeaux',
      mobileTabAddresses: 'Adresses',
      mobileTabSecurity: 'Sécurité'
    }
  }
}

describe('Tablet and Mobile Menu Translations', () => {
  it('should have correct Italian tablet menu translations', () => {
    const itNavigation = mockMessages.it.navigation

    expect(itNavigation.tabletShop).toBe('Shop')
    expect(itNavigation.tabletBuilder).toBe('Builder')
    expect(itNavigation.tabletBox).toBe('Box')
    expect(itNavigation.tabletGifts).toBe('Regali')
    expect(itNavigation.tabletAbout).toBe('Chi siamo')
  })

  it('should have correct German tablet menu translations', () => {
    const deNavigation = mockMessages.de.navigation

    expect(deNavigation.tabletShop).toBe('Shop')
    expect(deNavigation.tabletBuilder).toBe('Builder')
    expect(deNavigation.tabletBox).toBe('Bundles')
    expect(deNavigation.tabletGifts).toBe('Geschenke')
    expect(deNavigation.tabletAbout).toBe('Über uns')
  })

  it('should have correct French tablet menu translations', () => {
    const frNavigation = mockMessages.fr.navigation

    expect(frNavigation.tabletShop).toBe('Boutique')
    expect(frNavigation.tabletBuilder).toBe('Builder')
    expect(frNavigation.tabletBox).toBe('Coffrets')
    expect(frNavigation.tabletGifts).toBe('Cadeaux')
    expect(frNavigation.tabletAbout).toBe('À propos')
  })

  it('should have correct Italian mobile menu translations', () => {
    const itAccount = mockMessages.it.account

    expect(itAccount.mobileTabProfile).toBe('Profilo')
    expect(itAccount.mobileTabPersonalData).toBe('Dati')
    expect(itAccount.mobileTabOrders).toBe('Ordini')
    expect(itAccount.mobileTabGifts).toBe('Regali')
    expect(itAccount.mobileTabAddresses).toBe('Indirizzi')
    expect(itAccount.mobileTabSecurity).toBe('Sicurezza')
  })

  it('should have correct German mobile menu translations', () => {
    const deAccount = mockMessages.de.account

    expect(deAccount.mobileTabProfile).toBe('Profil')
    expect(deAccount.mobileTabPersonalData).toBe('Daten')
    expect(deAccount.mobileTabOrders).toBe('Bestellungen')
    expect(deAccount.mobileTabGifts).toBe('Geschenke')
    expect(deAccount.mobileTabAddresses).toBe('Adressen')
    expect(deAccount.mobileTabSecurity).toBe('Sicherheit')
  })

  it('should have correct French mobile menu translations', () => {
    const frAccount = mockMessages.fr.account

    expect(frAccount.mobileTabProfile).toBe('Profil')
    expect(frAccount.mobileTabPersonalData).toBe('Données')
    expect(frAccount.mobileTabOrders).toBe('Commandes')
    expect(frAccount.mobileTabGifts).toBe('Cadeaux')
    expect(frAccount.mobileTabAddresses).toBe('Adresses')
    expect(frAccount.mobileTabSecurity).toBe('Sécurité')
  })

  it('should have different translations for different languages', () => {
    // Tablet menu differences
    expect(mockMessages.it.navigation.tabletAbout).not.toBe(mockMessages.de.navigation.tabletAbout)
    expect(mockMessages.it.navigation.tabletAbout).not.toBe(mockMessages.fr.navigation.tabletAbout)
    expect(mockMessages.de.navigation.tabletAbout).not.toBe(mockMessages.fr.navigation.tabletAbout)

    // Mobile menu differences
    expect(mockMessages.it.account.mobileTabOrders).not.toBe(mockMessages.de.account.mobileTabOrders)
    expect(mockMessages.it.account.mobileTabOrders).not.toBe(mockMessages.fr.account.mobileTabOrders)
    expect(mockMessages.de.account.mobileTabOrders).not.toBe(mockMessages.fr.account.mobileTabOrders)
  })
})
