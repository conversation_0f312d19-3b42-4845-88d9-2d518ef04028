import { useState, useEffect, useMemo } from 'react'

interface FilterOptions {
  categories: string[]
  coffeeTypes: string[]
  brands: string[]
  blends: string[]
  compatibilities: string[]
}

interface UseDynamicFilterOptionsProps {
  category?: string
  coffeeType?: string
  brand?: string
}

interface UseDynamicFilterOptionsReturn {
  filterOptions: FilterOptions
  loading: boolean
  error: string | null
}

export function useDynamicFilterOptions({
  category,
  coffeeType,
  brand
}: UseDynamicFilterOptionsProps = {}): UseDynamicFilterOptionsReturn {
  const [filterOptions, setFilterOptions] = useState<FilterOptions>({
    categories: [],
    coffeeTypes: [],
    brands: [],
    blends: [],
    compatibilities: []
  })
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Create query parameters based on current filters
  const queryParams = useMemo(() => {
    const params = new URLSearchParams()
    if (category && category !== 'all') params.set('category', category)
    if (coffeeType && coffeeType !== 'all') params.set('coffee_type', coffeeType)
    if (brand && brand !== 'all') params.set('brand', brand)
    return params.toString()
  }, [category, coffeeType, brand])

  useEffect(() => {
    const fetchFilterOptions = async () => {
      try {
        setLoading(true)
        setError(null)
        
        const url = `/api/products/filter-options${queryParams ? `?${queryParams}` : ''}`
        const response = await fetch(url)
        
        if (!response.ok) {
          throw new Error('Failed to fetch filter options')
        }
        
        const data = await response.json()
        setFilterOptions(data)
        
      } catch (err) {
        console.error('Error fetching filter options:', err)
        setError(err instanceof Error ? err.message : 'Unknown error')
      } finally {
        setLoading(false)
      }
    }

    fetchFilterOptions()
  }, [queryParams])

  return {
    filterOptions,
    loading,
    error
  }
}
