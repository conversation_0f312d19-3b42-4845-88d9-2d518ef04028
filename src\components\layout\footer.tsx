'use client';

import Link from 'next/link';
import { useTranslations, useLocale } from 'next-intl';
import { Coffee, Mail } from 'lucide-react';


export function Footer() {
  const currentYear = new Date().getFullYear();
  const t = useTranslations('footer');
  const locale = useLocale();

  // Generate localized paths with proper locale handling
  const getLocalizedPath = (path: string) => {
    // Always include locale prefix
    return `/${locale}${path}`;
  };

  return (
    <footer className="bg-gradient-to-br from-muted/50 via-muted/30 to-background border-t shadow-soft">
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="space-y-4 animate-fade-in-up">
            <div className="flex items-center space-x-2 group">
              <Coffee className="h-6 w-6 text-primary group-hover:rotate-12 transition-transform-smooth" />
              <span className="text-lg font-bold bg-gradient-to-r from-foreground to-foreground/80 bg-clip-text text-transparent">PrimeCaffe</span>
            </div>
            <p className="text-sm text-muted-foreground">
              {t('description')}
            </p>
            <div className="flex space-x-4">
              <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                <Mail className="h-4 w-4" />
                <span><EMAIL></span>
              </div>
            </div>
          </div>

          {/* Quick Links */}
          <div className="space-y-4 animate-fade-in-up" style={{animationDelay: '0.1s'}}>
            <h3 className="text-sm font-semibold bg-gradient-to-r from-foreground to-foreground/80 bg-clip-text text-transparent">{t('shop.title')}</h3>
            <ul className="space-y-2 text-sm">
              <li>
                <Link href={getLocalizedPath('/shop')} className="text-muted-foreground hover:text-primary transition-all-smooth hover:scale-105 inline-block">
                  {t('shop.allProducts')}
                </Link>
              </li>
              <li>
                <Link href={getLocalizedPath('/coffee-box-builder')} className="text-muted-foreground hover:text-primary transition-all-smooth hover:scale-105 inline-block">
                  {t('shop.coffeeBoxBuilder')}
                </Link>
              </li>
              <li>
                <Link href={getLocalizedPath('/bundles')} className="text-muted-foreground hover:text-primary transition-all-smooth hover:scale-105 inline-block">
                  {t('shop.bundles')}
                </Link>
              </li>

            </ul>
          </div>

          {/* Customer Service */}
          <div className="space-y-4 animate-fade-in-up" style={{animationDelay: '0.2s'}}>
            <h3 className="text-sm font-semibold bg-gradient-to-r from-foreground to-foreground/80 bg-clip-text text-transparent">{t('customerService.title')}</h3>
            <ul className="space-y-2 text-sm">
              <li>
                <Link href={getLocalizedPath('/contact')} className="text-muted-foreground hover:text-primary transition-all-smooth hover:scale-105 inline-block">
                  {t('customerService.contact')}
                </Link>
              </li>
              <li>
                <Link href={getLocalizedPath('/shipping')} className="text-muted-foreground hover:text-primary transition-all-smooth hover:scale-105 inline-block">
                  {t('customerService.shipping')}
                </Link>
              </li>
              <li>
                <Link href={getLocalizedPath('/returns')} className="text-muted-foreground hover:text-primary transition-all-smooth hover:scale-105 inline-block">
                  {t('customerService.returns')}
                </Link>
              </li>
              <li>
                <Link href={getLocalizedPath('/faq')} className="text-muted-foreground hover:text-primary transition-all-smooth hover:scale-105 inline-block">
                  {t('customerService.faq')}
                </Link>
              </li>
              <li>
                <Link href={getLocalizedPath('/regali')} className="text-muted-foreground hover:text-primary transition-all-smooth hover:scale-105 inline-block">
                  {t('customerService.gifts')}
                </Link>
              </li>
            </ul>
          </div>

          {/* Legal */}
          <div className="space-y-4 animate-fade-in-up" style={{animationDelay: '0.3s'}}>
            <h3 className="text-sm font-semibold bg-gradient-to-r from-foreground to-foreground/80 bg-clip-text text-transparent">{t('legal.title')}</h3>
            <ul className="space-y-2 text-sm">
              <li>
                <Link href={getLocalizedPath('/privacy')} className="text-muted-foreground hover:text-primary transition-all-smooth hover:scale-105 inline-block">
                  {t('legal.privacy')}
                </Link>
              </li>
              <li>
                <Link href={getLocalizedPath('/terms')} className="text-muted-foreground hover:text-primary transition-all-smooth hover:scale-105 inline-block">
                  {t('legal.terms')}
                </Link>
              </li>
              <li>
                <Link href={getLocalizedPath('/cookies')} className="text-muted-foreground hover:text-primary transition-all-smooth hover:scale-105 inline-block">
                  {t('legal.cookies')}
                </Link>
              </li>
              <li>
                <Link href={getLocalizedPath('/imprint')} className="text-muted-foreground hover:text-primary transition-all-smooth hover:scale-105 inline-block">
                  {t('legal.imprint')}
                </Link>
              </li>
            </ul>
          </div>
        </div>

        <div className="mt-8 pt-8 border-t">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-sm text-muted-foreground">
              © {currentYear} PrimeCaffe. {t('copyright')}
            </p>
            <p className="text-sm text-muted-foreground mt-2 md:mt-0">
              {t('madeIn')}
            </p>
          </div>
        </div>
      </div>
    </footer>
  );
}
