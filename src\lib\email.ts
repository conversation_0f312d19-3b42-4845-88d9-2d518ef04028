import nodemailer from 'nodemailer';
import { formatCurrency, formatDateTime } from './utils';

// Interface for shipping address as stored in database
interface ShippingAddress {
  firstName: string;
  lastName: string;
  street: string;
  postalCode: string;
  city: string;
  country: string;
}

interface Product {
  title: string;
}

interface OrderItem {
  quantity: number;
  unit_price: number;
  total_price: number;
  product: Product;
}

interface Order {
  id: string;
  order_number?: string;
  email: string;
  status: string;
  subtotal: number;
  shipping_cost: number;
  discount_amount: number;
  total_amount: number;
  created_at: string;
  shipping_address: ShippingAddress;
  billing_address: ShippingAddress;
}

// Create transporter
const createTransporter = () => {
  console.log('📧 Email: Creating SMTP transporter...');

  // Check environment variables
  const requiredVars = {
    SMTP_HOST: process.env.SMTP_HOST,
    SMTP_USER: process.env.SMTP_USER,
    SMTP_PASS: process.env.SMTP_PASS,
    SMTP_FROM: process.env.SMTP_FROM,
    SMTP_FROM_NAME: process.env.SMTP_FROM_NAME
  };

  console.log('📧 Email: Environment variables check:', {
    SMTP_HOST: requiredVars.SMTP_HOST ? '✓ Set' : '✗ Missing',
    SMTP_USER: requiredVars.SMTP_USER ? '✓ Set' : '✗ Missing',
    SMTP_PASS: requiredVars.SMTP_PASS ? '✓ Set' : '✗ Missing',
    SMTP_FROM: requiredVars.SMTP_FROM ? '✓ Set' : '✗ Missing',
    SMTP_FROM_NAME: requiredVars.SMTP_FROM_NAME ? '✓ Set' : '✗ Missing',
    SMTP_PORT: process.env.SMTP_PORT || '587',
    SMTP_SECURE: process.env.SMTP_SECURE || 'false'
  });

  if (!process.env.SMTP_HOST || !process.env.SMTP_USER || !process.env.SMTP_PASS) {
    const error = 'SMTP configuration is incomplete';
    console.error('📧 Email: Configuration error:', error);
    throw new Error(error);
  }

  const config = {
    host: process.env.SMTP_HOST,
    port: parseInt(process.env.SMTP_PORT || '587'),
    secure: process.env.SMTP_SECURE === 'true',
    auth: {
      user: process.env.SMTP_USER,
      pass: process.env.SMTP_PASS,
    },
    debug: process.env.NODE_ENV === 'development',
    logger: process.env.NODE_ENV === 'development'
  };

  // Only log SMTP configuration in development
  if (process.env.NODE_ENV === 'development') {
    console.log('📧 Email: SMTP configuration:', {
      host: config.host,
      port: config.port,
      secure: config.secure,
      user: config.auth.user,
      debug: config.debug
    });
  }

  return nodemailer.createTransport(config);
};

// Test email function for debugging
export const testEmailConfiguration = async () => {
  console.log('📧 Email: Testing email configuration...');

  try {
    const transporter = createTransporter();
    console.log('📧 Email: Transporter created for test');

    // Test connection
    console.log('📧 Email: Testing SMTP connection...');
    const isConnected = await transporter.verify();
    console.log('📧 Email: SMTP connection test result:', isConnected);

    // Send test email
    const testMailOptions = {
      from: `${process.env.SMTP_FROM_NAME} <${process.env.SMTP_FROM}>`,
      to: process.env.NEXT_PUBLIC_COMPANY_EMAIL || '<EMAIL>',
      subject: 'Test Email - PrimeCaffe Configuration',
      html: `
        <h2>Test Email</h2>
        <p>This is a test email to verify the email configuration.</p>
        <p>Sent at: ${new Date().toISOString()}</p>
        <p>Environment: ${process.env.NODE_ENV}</p>
      `
    };

    console.log('📧 Email: Sending test email with options:', {
      from: testMailOptions.from,
      to: testMailOptions.to,
      subject: testMailOptions.subject
    });

    const result = await transporter.sendMail(testMailOptions);
    console.log('📧 Email: Test email sent successfully:', {
      messageId: result.messageId,
      response: result.response,
      accepted: result.accepted,
      rejected: result.rejected
    });

    return { success: true, result };

  } catch (error) {
    console.error('📧 Email: Test email failed:', error);
    console.error('📧 Email: Test error details:', {
      name: error instanceof Error ? error.name : 'Unknown',
      message: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      code: (error as { code?: string })?.code,
      command: (error as { command?: string })?.command,
      response: (error as { response?: string })?.response,
      responseCode: (error as { responseCode?: number })?.responseCode
    });
    return { success: false, error };
  }
};

// Helper function to get order confirmation email content based on locale
const getOrderConfirmationEmailContent = (locale: string, order: Order) => {
  switch (locale) {
    case 'it':
      return {
        title: 'Conferma Ordine - PrimeCaffe',
        subject: `Conferma Ordine #${order.order_number || order.id} - PrimeCaffe`,
        heading: 'Grazie per il tuo ordine!',
        greeting: 'Gentile Cliente,',
        confirmationText: 'abbiamo ricevuto il tuo ordine e lo stiamo elaborando.',
        orderDetailsTitle: 'Dettagli dell\'ordine:',
        orderNumber: 'Numero ordine:',
        orderDate: 'Data ordine:',
        totalAmount: 'Importo totale:',
        itemsTitle: 'Articoli ordinati:',
        shippingNotification: 'Ti informeremo via email non appena il tuo ordine sarà spedito.',
        questionsText: 'Per qualsiasi domanda, non esitare a contattarci.',
        closing: 'Cordiali saluti,<br>Il Team PrimeCaffe'
      };

    case 'fr':
      return {
        title: 'Confirmation de Commande - PrimeCaffe',
        subject: `Confirmation de Commande #${order.order_number || order.id} - PrimeCaffe`,
        heading: 'Merci pour votre commande !',
        greeting: 'Cher/Chère Client(e),',
        confirmationText: 'nous avons reçu votre commande et nous la traitons.',
        orderDetailsTitle: 'Détails de la commande :',
        orderNumber: 'Numéro de commande :',
        orderDate: 'Date de commande :',
        totalAmount: 'Montant total :',
        itemsTitle: 'Articles commandés :',
        shippingNotification: 'Nous vous informerons par email dès que votre commande sera expédiée.',
        questionsText: 'Pour toute question, n\'hésitez pas à nous contacter.',
        closing: 'Cordialement,<br>L\'équipe PrimeCaffe'
      };

    default: // 'de'
      return {
        title: 'Bestellbestätigung - PrimeCaffe',
        subject: `Bestellbestätigung #${order.order_number || order.id} - PrimeCaffe`,
        heading: 'Vielen Dank für Ihre Bestellung!',
        greeting: 'Liebe/r Kunde/in,',
        confirmationText: 'wir haben Ihre Bestellung erhalten und bearbeiten sie.',
        orderDetailsTitle: 'Bestelldetails:',
        orderNumber: 'Bestellnummer:',
        orderDate: 'Bestelldatum:',
        totalAmount: 'Gesamtbetrag:',
        itemsTitle: 'Bestellte Artikel:',
        shippingNotification: 'Wir werden Sie per E-Mail informieren, sobald Ihre Bestellung versandt wurde.',
        questionsText: 'Bei Fragen stehen wir Ihnen gerne zur Verfügung.',
        closing: 'Mit freundlichen Grüssen,<br>Ihr PrimeCaffe Team'
      };
  }
};

// Email templates
export const sendOrderConfirmationEmail = async (
  order: Order & {
    items?: (OrderItem & { product?: { title: string }, products?: { title: string } })[]
    order_items?: (OrderItem & { product?: { title: string }, products?: { title: string } })[]
  },
  locale: string = 'de'
) => {
  console.log('📧 Email: Starting order confirmation email send...');
  console.log('📧 Email: Order details:', {
    orderId: order.id,
    email: order.email,
    itemsCount:
      (order.items || (order as unknown as { order_items?: OrderItem[] }).order_items)?.length || 0,
    totalAmount: order.total_amount
  });

  try {
    const transporter = createTransporter();
    console.log('📧 Email: Transporter created successfully');

    // Test connection
    console.log('📧 Email: Testing SMTP connection...');
    await transporter.verify();
    console.log('📧 Email: SMTP connection verified successfully');

    const content = getOrderConfirmationEmailContent(locale, order);

    const itemsList =
      order.items || (order as unknown as { order_items?: OrderItem[] }).order_items || []
    const itemsHtml = itemsList
      .map(
        (item) => `
          <tr>
            <td style="padding: 8px; border-bottom: 1px solid #eee;">${item.product?.title || (item as unknown as { products?: { title?: string } }).products?.title || 'Product'}</td>
            <td style="padding: 8px; border-bottom: 1px solid #eee; text-align: center;">${item.quantity}</td>
            <td style="padding: 8px; border-bottom: 1px solid #eee; text-align: right;">${formatCurrency(item.unit_price)}</td>
            <td style="padding: 8px; border-bottom: 1px solid #eee; text-align: right;">${formatCurrency(item.total_price)}</td>
          </tr>
        `
      )
      .join('');

    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>${content.title}</title>
      </head>
      <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
        <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="text-align: center; margin-bottom: 30px;">
            <h1 style="color: #d97706;">PrimeCaffe</h1>
          </div>

          <h2>${content.heading}</h2>

          <p>${content.greeting}</p>

          <p>${content.confirmationText}</p>

          <div style="background: #f9f9f9; padding: 20px; margin: 20px 0; border-radius: 5px;">
            <h3>${content.orderNumber} ${order.order_number || order.id}</h3>
            <p><strong>${content.orderDate}</strong> ${formatDateTime(order.created_at)}</p>
            <p><strong>${content.totalAmount}</strong> ${formatCurrency(order.total_amount)}</p>
          </div>

          <h3>${content.itemsTitle}</h3>
        <table style="width: 100%; border-collapse: collapse; margin: 20px 0;">
          <thead>
            <tr style="background: #f5f5f5;">
              <th style="padding: 12px; text-align: left; border-bottom: 2px solid #ddd;">Artikel</th>
              <th style="padding: 12px; text-align: center; border-bottom: 2px solid #ddd;">Anzahl</th>
              <th style="padding: 12px; text-align: right; border-bottom: 2px solid #ddd;">Preis</th>
              <th style="padding: 12px; text-align: right; border-bottom: 2px solid #ddd;">Gesamt</th>
            </tr>
          </thead>
          <tbody>
            ${itemsHtml}
          </tbody>
        </table>
        
        <div style="text-align: right; margin: 20px 0;">
          <p><strong>Zwischensumme: ${formatCurrency(order.subtotal)}</strong></p>
          <p><strong>Versandkosten: ${formatCurrency(order.shipping_cost)}</strong></p>
          ${order.discount_amount > 0 ? `<p><strong>Rabatt: -${formatCurrency(order.discount_amount)}</strong></p>` : ''}
          <p style="font-size: 18px; color: #d97706;"><strong>Gesamtbetrag: ${formatCurrency(order.total_amount)}</strong></p>
        </div>
        
        <div style="background: #f0f9ff; padding: 20px; margin: 20px 0; border-radius: 5px;">
          <h3>Lieferadresse:</h3>
          <p>
            ${(order.shipping_address as ShippingAddress).firstName} ${(order.shipping_address as ShippingAddress).lastName}<br>
            ${(order.shipping_address as ShippingAddress).street}<br>
            ${(order.shipping_address as ShippingAddress).postalCode} ${(order.shipping_address as ShippingAddress).city}<br>
            ${(order.shipping_address as ShippingAddress).country}
          </p>
        </div>
        
        <p>${content.shippingNotification}</p>

        <p>${content.questionsText}</p>

        <p>${content.closing}</p>
        
        <hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;">
        
        <div style="text-align: center; color: #666; font-size: 12px;">
          <p>BL Glamour Sagl<br>
          ${process.env.NEXT_PUBLIC_COMPANY_ADDRESS}<br>
          E-Mail: ${process.env.NEXT_PUBLIC_COMPANY_EMAIL}<br>
          Telefon: ${process.env.NEXT_PUBLIC_COMPANY_PHONE}</p>
        </div>
      </div>
    </body>
    </html>
  `;

    const mailOptions = {
      from: `${process.env.SMTP_FROM_NAME} <${process.env.SMTP_FROM}>`,
      to: order.email,
      subject: content.subject,
      html,
    };

    console.log('📧 Email: Sending order confirmation email with options:', {
      from: mailOptions.from,
      to: mailOptions.to,
      subject: mailOptions.subject,
      htmlLength: html.length
    });

    const result = await transporter.sendMail(mailOptions);
    console.log('📧 Email: Order confirmation email sent successfully:', {
      messageId: result.messageId,
      response: result.response,
      accepted: result.accepted,
      rejected: result.rejected
    });

    return result;

  } catch (error) {
    console.error('📧 Email: Error sending order confirmation email:', error);
    console.error('📧 Email: Error details:', {
      name: error instanceof Error ? error.name : 'Unknown',
      message: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      code: (error as { code?: string })?.code,
      command: (error as { command?: string })?.command,
      response: (error as { response?: string })?.response,
      responseCode: (error as { responseCode?: number })?.responseCode
    });
    throw error;
  }
};

// Helper function to get shipping email content based on locale
const getShippingEmailContent = (locale: string, order: Order) => {
  switch (locale) {
    case 'it':
      return {
        title: 'Il tuo ordine è stato spedito - PrimeCaffe',
        subject: `Il tuo ordine #${order.order_number || order.id} è stato spedito - PrimeCaffe`,
        heading: 'Il tuo ordine è in viaggio!',
        greeting: 'Gentile Cliente,',
        goodNews: 'buone notizie! Il tuo ordine è stato spedito ed è già in viaggio verso di te.',
        shippingInfoTitle: 'Informazioni di spedizione:',
        orderNumber: 'Numero ordine:',
        trackingNumber: 'Numero di tracciamento:',
        shippingDate: 'Data di spedizione:',
        deliveryAddressTitle: 'Indirizzo di consegna:',
        trackingInfo: 'Puoi tracciare la tua spedizione con il numero di tracciamento sopra indicato direttamente sul sito della Posta Svizzera: <a href="https://www.post.ch/it" target="_blank" style="color: #d97706; text-decoration: none;">www.post.ch</a>',
        enjoyOrder: 'Speriamo che tu possa goderti il tuo ordine!',
        closing: 'Cordiali saluti,<br>Il Team PrimeCaffe'
      };

    case 'fr':
      return {
        title: 'Votre commande a été expédiée - PrimeCaffe',
        subject: `Votre commande #${order.order_number || order.id} a été expédiée - PrimeCaffe`,
        heading: 'Votre commande est en route !',
        greeting: 'Cher/Chère Client(e),',
        goodNews: 'bonne nouvelle ! Votre commande a été expédiée et est déjà en route vers vous.',
        shippingInfoTitle: 'Informations d\'expédition :',
        orderNumber: 'Numéro de commande :',
        trackingNumber: 'Numéro de suivi :',
        shippingDate: 'Date d\'expédition :',
        deliveryAddressTitle: 'Adresse de livraison :',
        trackingInfo: 'Vous pouvez suivre votre envoi avec le numéro de suivi indiqué ci-dessus directement sur le site de La Poste Suisse : <a href="https://www.post.ch/fr" target="_blank" style="color: #d97706; text-decoration: none;">www.post.ch</a>',
        enjoyOrder: 'Nous espérons que vous apprécierez votre commande !',
        closing: 'Cordialement,<br>L\'équipe PrimeCaffe'
      };

    default: // 'de'
      return {
        title: 'Ihre Bestellung wurde versandt - PrimeCaffe',
        subject: `Ihre Bestellung #${order.order_number || order.id} wurde versandt - PrimeCaffe`,
        heading: 'Ihre Bestellung ist unterwegs!',
        greeting: 'Liebe/r Kunde/in,',
        goodNews: 'gute Nachrichten! Ihre Bestellung wurde versandt und ist bereits auf dem Weg zu Ihnen.',
        shippingInfoTitle: 'Versandinformationen:',
        orderNumber: 'Bestellnummer:',
        trackingNumber: 'Sendungsnummer:',
        shippingDate: 'Versanddatum:',
        deliveryAddressTitle: 'Lieferadresse:',
        trackingInfo: 'Sie können Ihre Sendung mit der oben angegebenen Sendungsnummer direkt auf der Website der Schweizer Post verfolgen: <a href="https://www.post.ch/de" target="_blank" style="color: #d97706; text-decoration: none;">www.post.ch</a>',
        enjoyOrder: 'Wir hoffen, dass Sie Ihre Bestellung geniessen werden!',
        closing: 'Mit freundlichen Grüssen,<br>Ihr PrimeCaffe Team'
      };
  }
};

export const sendShippingNotificationEmail = async (
  order: Order,
  trackingNumber: string,
  locale: string = 'de'
) => {
  console.log('📧 Email: Starting shipping notification email send...');
  console.log('📧 Email: Shipping notification details:', {
    orderId: order.id,
    email: order.email,
    trackingNumber: trackingNumber,
    totalAmount: order.total_amount,
    locale: locale
  });

  try {
    const transporter = createTransporter();
    console.log('📧 Email: Shipping notification transporter created successfully');

    const content = getShippingEmailContent(locale, order);
    const shippingAddress = order.shipping_address as ShippingAddress;

    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>${content.title}</title>
      </head>
      <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
        <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="text-align: center; margin-bottom: 30px;">
            <h1 style="color: #d97706;">PrimeCaffe</h1>
          </div>

          <h2>${content.heading}</h2>

          <p>${content.greeting}</p>

          <p>${content.goodNews}</p>

          <div style="background: #f0f9ff; padding: 20px; margin: 20px 0; border-radius: 5px;">
            <h3>${content.shippingInfoTitle}</h3>
            <p><strong>${content.orderNumber}</strong> ${order.order_number || order.id}</p>
            <p><strong>${content.trackingNumber}</strong> ${trackingNumber}</p>
            <p><strong>${content.shippingDate}</strong> ${formatDateTime(new Date().toISOString())}</p>
          </div>

          <div style="background: #f9f9f9; padding: 20px; margin: 20px 0; border-radius: 5px;">
            <h3>${content.deliveryAddressTitle}</h3>
            <p>
              ${shippingAddress.firstName} ${shippingAddress.lastName}<br>
              ${shippingAddress.street}<br>
              ${shippingAddress.postalCode} ${shippingAddress.city}<br>
              ${shippingAddress.country}
            </p>
          </div>

          <p>${content.trackingInfo}</p>

          <p>${content.enjoyOrder}</p>

          <p>${content.closing}</p>

          <hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;">

          <div style="text-align: center; color: #666; font-size: 12px;">
            <p>BL Glamour Sagl<br>
            ${process.env.NEXT_PUBLIC_COMPANY_ADDRESS}<br>
            E-Mail: ${process.env.NEXT_PUBLIC_COMPANY_EMAIL}<br>
            Telefon: ${process.env.NEXT_PUBLIC_COMPANY_PHONE}</p>
          </div>
        </div>
      </body>
      </html>
    `;

    const shippingMailOptions = {
      from: `${process.env.SMTP_FROM_NAME} <${process.env.SMTP_FROM}>`,
      to: order.email,
      subject: content.subject,
      html,
    };

    console.log('📧 Email: Sending shipping notification email with options:', {
      from: shippingMailOptions.from,
      to: shippingMailOptions.to,
      subject: shippingMailOptions.subject,
      htmlLength: html.length
    });

    const result = await transporter.sendMail(shippingMailOptions);
    console.log('📧 Email: Shipping notification email sent successfully:', {
      messageId: result.messageId,
      response: result.response,
      accepted: result.accepted,
      rejected: result.rejected
    });

    return result;

  } catch (error) {
    console.error('📧 Email: Error sending shipping notification email:', error);
    console.error('📧 Email: Shipping notification error details:', {
      name: error instanceof Error ? error.name : 'Unknown',
      message: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      code: (error as { code?: string })?.code,
      command: (error as { command?: string })?.command,
      response: (error as { response?: string })?.response,
      responseCode: (error as { responseCode?: number })?.responseCode
    });
    throw error;
  }
};

export const sendAdminOrderNotificationEmail = async (
  order: Order & {
    items?: (OrderItem & { product?: { title: string }, products?: { title: string } })[]
    order_items?: (OrderItem & { product?: { title: string }, products?: { title: string } })[]
  }
) => {
  console.log('📧 Email: Starting admin order notification email send...');
  console.log('📧 Email: Admin notification details:', {
    orderId: order.id,
    customerEmail: order.email,
    adminEmail: process.env.NEXT_PUBLIC_COMPANY_EMAIL,
    itemsCount:
      (order.items || (order as unknown as { order_items?: OrderItem[] }).order_items)?.length || 0,
    totalAmount: order.total_amount
  });

  try {
    const transporter = createTransporter();
    console.log('📧 Email: Admin notification transporter created successfully');

  const itemsList =
    order.items || (order as unknown as { order_items?: OrderItem[] }).order_items || []
  const itemsText = itemsList
    .map((item) => `- ${item.product?.title || (item as unknown as { products?: { title?: string } }).products?.title || 'Product'} (${item.quantity}x ${formatCurrency(item.unit_price)})`)
    .join('\n');

  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>Neue Bestellung - PrimeCaffe Admin</title>
    </head>
    <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
      <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
        <h2>Neue Bestellung eingegangen</h2>
        
        <div style="background: #f9f9f9; padding: 20px; margin: 20px 0; border-radius: 5px;">
          <h3>Bestelldetails:</h3>
          <p><strong>Bestellnummer:</strong> ${order.order_number || order.id}</p>
          <p><strong>Kunde:</strong> ${order.email}</p>
          <p><strong>Gesamtbetrag:</strong> ${formatCurrency(order.total_amount)}</p>
          <p><strong>Bestelldatum:</strong> ${formatDateTime(order.created_at)}</p>
        </div>
        
        <h3>Bestellte Artikel:</h3>
        <pre style="background: #f5f5f5; padding: 15px; border-radius: 5px;">${itemsText}</pre>
        
        <p><a href="${process.env.NEXT_PUBLIC_APP_URL}/admin/orders/${order.id}" style="background: #d97706; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Bestellung anzeigen</a></p>
      </div>
    </body>
    </html>
  `;

    const adminMailOptions = {
      from: `${process.env.SMTP_FROM_NAME} <${process.env.SMTP_FROM}>`,
      to: process.env.NEXT_PUBLIC_COMPANY_EMAIL,
      subject: `Neue Bestellung #${order.order_number || order.id} - PrimeCaffe`,
      html,
    };

    console.log('📧 Email: Sending admin notification email with options:', {
      from: adminMailOptions.from,
      to: adminMailOptions.to,
      subject: adminMailOptions.subject,
      htmlLength: html.length
    });

    const result = await transporter.sendMail(adminMailOptions);
    console.log('📧 Email: Admin notification email sent successfully:', {
      messageId: result.messageId,
      response: result.response,
      accepted: result.accepted,
      rejected: result.rejected
    });

    return result;

  } catch (error) {
    console.error('📧 Email: Error sending admin notification email:', error);
    console.error('📧 Email: Admin notification error details:', {
      name: error instanceof Error ? error.name : 'Unknown',
      message: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      code: (error as { code?: string })?.code,
      command: (error as { command?: string })?.command,
      response: (error as { response?: string })?.response,
      responseCode: (error as { responseCode?: number })?.responseCode
    });
    throw error;
  }
};

// Contact form notification email
interface ContactFormData {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  subject: string;
  message: string;
}

// Helper function to get cart recovery email content based on locale
const getCartRecoveryEmailContent = (locale: string, order: Pick<Order, 'id' | 'email' | 'total_amount' | 'order_number'>) => {
  switch (locale) {
    case 'it':
      return {
        title: 'Il tuo carrello ti aspetta - PrimeCaffe',
        subject: `Il tuo carrello ti aspetta - Completa il tuo ordine #${order.order_number || order.id}`,
        heading: 'Il tuo caffè ti aspetta! ☕',
        greeting: 'Ciao,',
        reminderText: 'hai lasciato alcuni fantastici prodotti nel tuo carrello. Non perdere l\'occasione di completare il tuo ordine!',
        orderDetailsTitle: 'Il tuo ordine:',
        orderNumber: 'Numero ordine:',
        totalAmount: 'Totale:',
        gamificationTitle: '🎁 Sapevi che con PrimeCaffe guadagni punti fedeltà?',
        gamificationText: 'Completando questo ordine guadagnerai punti che ti permetteranno di salire di livello e ricevere regali esclusivi. Più ordini fai, più vantaggi ottieni!',
        benefitsTitle: 'I tuoi vantaggi:',
        benefit1: '• Punti fedeltà su ogni acquisto',
        benefit2: '• Regali esclusivi raggiungendo nuovi livelli',
        benefit3: '• Sconti progressivi per clienti fedeli',
        ctaText: 'Completa il tuo ordine ora',
        urgencyText: 'Questo carrello sarà disponibile ancora per poco tempo.',
        questionsText: 'Hai domande? Siamo qui per aiutarti.',
        closing: 'A presto,<br>Il team PrimeCaffe'
      };

    case 'fr':
      return {
        title: 'Votre panier vous attend - PrimeCaffe',
        subject: `Votre panier vous attend - Complétez votre commande #${order.order_number || order.id}`,
        heading: 'Votre café vous attend ! ☕',
        greeting: 'Bonjour,',
        reminderText: 'vous avez laissé de fantastiques produits dans votre panier. Ne manquez pas l\'occasion de compléter votre commande !',
        orderDetailsTitle: 'Votre commande :',
        orderNumber: 'Numéro de commande :',
        totalAmount: 'Total :',
        gamificationTitle: '🎁 Saviez-vous qu\'avec PrimeCaffe vous gagnez des points de fidélité ?',
        gamificationText: 'En complétant cette commande, vous gagnerez des points qui vous permettront de monter de niveau et de recevoir des cadeaux exclusifs. Plus vous commandez, plus vous obtenez d\'avantages !',
        benefitsTitle: 'Vos avantages :',
        benefit1: '• Points de fidélité sur chaque achat',
        benefit2: '• Cadeaux exclusifs en atteignant de nouveaux niveaux',
        benefit3: '• Remises progressives pour les clients fidèles',
        ctaText: 'Complétez votre commande maintenant',
        urgencyText: 'Ce panier sera disponible encore pour peu de temps.',
        questionsText: 'Des questions ? Nous sommes là pour vous aider.',
        closing: 'À bientôt,<br>L\'équipe PrimeCaffe'
      };

    default: // 'de'
      return {
        title: 'Ihr Warenkorb wartet auf Sie - PrimeCaffe',
        subject: `Ihr Warenkorb wartet auf Sie - Vervollständigen Sie Ihre Bestellung #${order.order_number || order.id}`,
        heading: 'Ihr Kaffee wartet auf Sie! ☕',
        greeting: 'Hallo,',
        reminderText: 'Sie haben fantastische Produkte in Ihrem Warenkorb gelassen. Verpassen Sie nicht die Gelegenheit, Ihre Bestellung abzuschließen!',
        orderDetailsTitle: 'Ihre Bestellung:',
        orderNumber: 'Bestellnummer:',
        totalAmount: 'Gesamtbetrag:',
        gamificationTitle: '🎁 Wussten Sie, dass Sie mit PrimeCaffe Treuepunkte sammeln?',
        gamificationText: 'Durch den Abschluss dieser Bestellung sammeln Sie Punkte, mit denen Sie Stufen aufsteigen und exklusive Geschenke erhalten können. Je mehr Sie bestellen, desto mehr Vorteile erhalten Sie!',
        benefitsTitle: 'Ihre Vorteile:',
        benefit1: '• Treuepunkte bei jedem Kauf',
        benefit2: '• Exklusive Geschenke beim Erreichen neuer Stufen',
        benefit3: '• Progressive Rabatte für treue Kunden',
        ctaText: 'Bestellung jetzt abschließen',
        urgencyText: 'Dieser Warenkorb ist nur noch für kurze Zeit verfügbar.',
        questionsText: 'Haben Sie Fragen? Wir sind hier, um zu helfen.',
        closing: 'Bis bald,<br>Ihr PrimeCaffe Team'
      };
  }
};

// Cart recovery email template
export const sendCartRecoveryEmail = async (
  order: Pick<Order, 'id' | 'email' | 'total_amount' | 'order_number'>,
  locale: string = 'de'
) => {
  console.log('📧 Email: Starting cart recovery email send...');
  console.log('📧 Email: Order details:', {
    orderId: order.id,
    email: order.email,
    totalAmount: order.total_amount,
    locale
  });

  try {
    const transporter = createTransporter();
    console.log('📧 Email: Cart recovery transporter created successfully');

    // Test connection
    console.log('📧 Email: Testing SMTP connection...');
    await transporter.verify();
    console.log('📧 Email: SMTP connection verified successfully');

    const content = getCartRecoveryEmailContent(locale, order);
    const checkoutUrl = `${process.env.NEXT_PUBLIC_APP_URL}/${locale}/checkout?recovery=${order.id}`;

    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>${content.title}</title>
      </head>
      <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; background-color: #f9f9f9;">
        <div style="max-width: 600px; margin: 0 auto; background-color: white; border-radius: 10px; overflow: hidden; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
          <!-- Header -->
          <div style="background: linear-gradient(135deg, #d97706 0%, #f59e0b 100%); padding: 30px 20px; text-align: center;">
            <h1 style="color: white; margin: 0; font-size: 28px; font-weight: bold;">PrimeCaffe</h1>
          </div>

          <!-- Content -->
          <div style="padding: 40px 30px;">
            <h2 style="color: #d97706; margin-bottom: 20px; font-size: 24px;">${content.heading}</h2>

            <p style="margin-bottom: 20px; font-size: 16px;">${content.greeting}</p>

            <p style="margin-bottom: 30px; font-size: 16px;">${content.reminderText}</p>

            <!-- Order Details -->
            <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 30px;">
              <h3 style="color: #333; margin-bottom: 15px; font-size: 18px;">${content.orderDetailsTitle}</h3>
              <p style="margin: 5px 0;"><strong>${content.orderNumber}</strong> ${order.order_number || order.id}</p>
              <p style="margin: 5px 0;"><strong>${content.totalAmount}</strong> ${new Intl.NumberFormat('de-CH', { style: 'currency', currency: 'CHF' }).format(order.total_amount)}</p>
            </div>

            <!-- CTA Button -->
            <div style="text-align: center; margin: 30px 0;">
              <a href="${checkoutUrl}" style="background: linear-gradient(135deg, #d97706 0%, #f59e0b 100%); color: white; padding: 15px 30px; text-decoration: none; border-radius: 25px; font-weight: bold; font-size: 16px; display: inline-block; box-shadow: 0 4px 15px rgba(217, 119, 6, 0.3); transition: all 0.3s ease;">${content.ctaText}</a>
            </div>

            <!-- Gamification Section -->
            <div style="background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%); padding: 25px; border-radius: 10px; margin: 30px 0; border-left: 4px solid #0ea5e9;">
              <h3 style="color: #0369a1; margin-bottom: 15px; font-size: 18px;">${content.gamificationTitle}</h3>
              <p style="margin-bottom: 20px; color: #1e40af;">${content.gamificationText}</p>

              <div style="margin-top: 15px;">
                <h4 style="color: #0369a1; margin-bottom: 10px; font-size: 16px;">${content.benefitsTitle}</h4>
                <p style="margin: 5px 0; color: #1e40af;">${content.benefit1}</p>
                <p style="margin: 5px 0; color: #1e40af;">${content.benefit2}</p>
                <p style="margin: 5px 0; color: #1e40af;">${content.benefit3}</p>
              </div>
            </div>

            <!-- Urgency -->
            <div style="background-color: #fef3c7; padding: 15px; border-radius: 8px; border-left: 4px solid #f59e0b; margin: 20px 0;">
              <p style="margin: 0; color: #92400e; font-weight: 500;">⏰ ${content.urgencyText}</p>
            </div>

            <p style="margin-top: 30px;">${content.questionsText}</p>

            <p style="margin-top: 20px;">${content.closing}</p>
          </div>

          <!-- Footer -->
          <div style="background-color: #f8f9fa; padding: 20px; text-align: center; border-top: 1px solid #e9ecef;">
            <p style="margin: 0; color: #666; font-size: 12px;">
              BL Glamour Sagl<br>
              ${process.env.NEXT_PUBLIC_COMPANY_ADDRESS}<br>
              E-Mail: ${process.env.NEXT_PUBLIC_COMPANY_EMAIL}<br>
              Telefon: ${process.env.NEXT_PUBLIC_COMPANY_PHONE}
            </p>
          </div>
        </div>
      </body>
      </html>
    `;

    const mailOptions = {
      from: `${process.env.SMTP_FROM_NAME} <${process.env.SMTP_FROM}>`,
      to: order.email,
      subject: content.subject,
      html,
    };

    console.log('📧 Email: Sending cart recovery email with options:', {
      from: mailOptions.from,
      to: mailOptions.to,
      subject: mailOptions.subject,
      htmlLength: html.length
    });

    const result = await transporter.sendMail(mailOptions);
    console.log('📧 Email: Cart recovery email sent successfully:', result.messageId);

    return {
      success: true,
      messageId: result.messageId,
    };

  } catch (error) {
    console.error('📧 Email: Error sending cart recovery email:', error);
    throw error;
  }
};

export const sendContactFormNotificationEmail = async (formData: ContactFormData) => {
  console.log('📧 Email: Starting contact form notification email send...');
  console.log('📧 Email: Contact form details:', {
    name: `${formData.firstName} ${formData.lastName}`,
    email: formData.email,
    subject: formData.subject,
    adminEmail: process.env.NEXT_PUBLIC_COMPANY_EMAIL
  });

  try {
    const transporter = createTransporter();
    console.log('📧 Email: Contact form notification transporter created successfully');

    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Neue Kontaktanfrage - PrimeCaffe</title>
      </head>
      <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
        <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="text-align: center; margin-bottom: 30px;">
            <h1 style="color: #d97706;">PrimeCaffe</h1>
            <h2 style="color: #333; margin-top: 10px;">Neue Kontaktanfrage</h2>
          </div>

          <div style="background: #f9f9f9; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
            <h3 style="margin-top: 0; color: #d97706;">Kontaktdaten:</h3>
            <p><strong>Name:</strong> ${formData.firstName} ${formData.lastName}</p>
            <p><strong>E-Mail:</strong> <a href="mailto:${formData.email}" style="color: #d97706;">${formData.email}</a></p>
            ${formData.phone ? `<p><strong>Telefon:</strong> ${formData.phone}</p>` : ''}
            <p><strong>Betreff:</strong> ${formData.subject}</p>
          </div>

          <div style="background: #fff; padding: 20px; border: 1px solid #ddd; border-radius: 8px;">
            <h3 style="margin-top: 0; color: #d97706;">Nachricht:</h3>
            <div style="white-space: pre-wrap; background: #f5f5f5; padding: 15px; border-radius: 5px; border-left: 4px solid #d97706;">
${formData.message}
            </div>
          </div>

          <div style="margin-top: 30px; padding: 15px; background: #e7f3ff; border-radius: 8px; border-left: 4px solid #0066cc;">
            <p style="margin: 0; color: #0066cc;"><strong>Hinweis:</strong> Diese Nachricht wurde über das Kontaktformular auf der PrimeCaffe Website gesendet.</p>
          </div>

          <hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;">

          <div style="text-align: center; color: #666; font-size: 12px;">
            <p>BL Glamour Sagl<br>
            Via Industria 20, 6963 Pregassona<br>
            E-Mail: <EMAIL><br>
            Telefon: +41 79 342 65 74</p>
          </div>
        </div>
      </body>
      </html>
    `;

    const mailOptions = {
      from: `${process.env.SMTP_FROM_NAME} <${process.env.SMTP_FROM}>`,
      to: process.env.NEXT_PUBLIC_COMPANY_EMAIL || '<EMAIL>',
      subject: `Neue Kontaktanfrage: ${formData.subject} - PrimeCaffe`,
      html,
      replyTo: formData.email // Allow admin to reply directly to the customer
    };

    console.log('📧 Email: Sending contact form notification email with options:', {
      from: mailOptions.from,
      to: mailOptions.to,
      subject: mailOptions.subject,
      replyTo: mailOptions.replyTo,
      htmlLength: html.length
    });

    const result = await transporter.sendMail(mailOptions);
    console.log('📧 Email: Contact form notification email sent successfully:', {
      messageId: result.messageId,
      response: result.response,
      accepted: result.accepted,
      rejected: result.rejected
    });

    return result;
  } catch (error) {
    console.error('📧 Email: Error sending contact form notification email:', error);
    throw error;
  }
};
