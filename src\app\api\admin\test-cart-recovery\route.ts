import { NextResponse } from 'next/server'
import { sendCartRecoveryEmail } from '@/lib/email'

export async function POST(request: Request) {
  try {
    // Check if this is a development environment
    if (process.env.NODE_ENV !== 'development') {
      console.log('🧪 Cart Recovery Test: Only available in development')
      return NextResponse.json(
        { error: 'Test endpoint only available in development environment' },
        { status: 403 }
      )
    }

    // Parse request body to get language preference
    const body = await request.json().catch(() => ({}))
    const selectedLanguage = body.language

    console.log('🧪 Cart Recovery Test: Starting cart recovery email test...', {
      selectedLanguage: selectedLanguage || 'all languages'
    })

    // Create a test order for cart recovery
    const testOrder = {
      id: 'test-cart-recovery-' + Date.now(),
      email: process.env.TEST_USER_EMAIL || '<EMAIL>',
      order_number: 'TEST-CR-001',
      total_amount: 45.80,
      subtotal: 45.80,
      shipping_cost: 0,
      discount_amount: 0,
      status: 'pending',
      payment_status: 'pending',
      created_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
      user_id: null,
      shipping_address: {
        firstName: 'Test',
        lastName: 'User',
        street: 'Test Street 1',
        city: 'Zurich',
        postalCode: '8001',
        country: 'CH'
      },
      billing_address: {
        firstName: 'Test',
        lastName: 'User',
        street: 'Test Street 1',
        city: 'Zurich',
        postalCode: '8001',
        country: 'CH'
      }
    }

    console.log('🧪 Cart Recovery Test: Test order data:', {
      id: testOrder.id,
      email: testOrder.email,
      order_number: testOrder.order_number,
      total_amount: testOrder.total_amount
    })

    const results: {
      errors: string[]
      testOrder: typeof testOrder
      cartRecoveryEmails: {
        de?: { success: boolean; messageId?: string; error?: string }
        fr?: { success: boolean; messageId?: string; error?: string }
        it?: { success: boolean; messageId?: string; error?: string }
      }
    } = {
      errors: [],
      testOrder,
      cartRecoveryEmails: {}
    }

    // Test cart recovery emails in selected language(s)
    const locales = selectedLanguage ? [selectedLanguage] : ['de', 'fr', 'it']

    for (const locale of locales) {
      try {
        console.log(`🧪 Cart Recovery Test: Testing cart recovery email in ${locale}`)
        const result = await sendCartRecoveryEmail(testOrder, locale)
        results.cartRecoveryEmails[locale as keyof typeof results.cartRecoveryEmails] = {
          success: true,
          messageId: result.messageId
        }
        console.log(`🧪 Cart Recovery Test: Cart recovery email (${locale}) sent successfully`)
      } catch (error) {
        console.error(`🧪 Cart Recovery Test: Cart recovery email (${locale}) failed:`, error)
        results.cartRecoveryEmails[locale as keyof typeof results.cartRecoveryEmails] = {
          success: false,
          error: error instanceof Error ? error.message : String(error)
        }
        results.errors.push(`Cart recovery email (${locale}) failed: ${error instanceof Error ? error.message : String(error)}`)
      }
    }

    // Summary
    const successCount = Object.values(results.cartRecoveryEmails).filter(result => result.success).length
    const totalTests = Object.keys(results.cartRecoveryEmails).length

    console.log(`🧪 Cart Recovery Test: Test completed. ${successCount}/${totalTests} emails sent successfully`)

    return NextResponse.json({
      success: results.errors.length === 0,
      message: `Cart recovery email test completed. ${successCount}/${totalTests} emails sent successfully.`,
      results,
      summary: {
        totalTests,
        successCount,
        errorCount: results.errors.length,
        testEmail: testOrder.email
      }
    })

  } catch (error) {
    console.error('🧪 Cart Recovery Test: Error in test:', error)
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    )
  }
}

// GET method to check test availability
export async function GET() {
  if (process.env.NODE_ENV !== 'development') {
    return NextResponse.json(
      { error: 'Test endpoint only available in development environment' },
      { status: 403 }
    )
  }

  return NextResponse.json({
    message: 'Cart recovery test endpoint is available',
    usage: 'Send a POST request to this endpoint to test cart recovery emails',
    testEmail: process.env.TEST_USER_EMAIL || '<EMAIL>',
    supportedLocales: ['de', 'fr', 'it']
  })
}
