import { createClient } from '@/lib/supabase/server'
import { notFound } from 'next/navigation'
import { formatCurrency } from '@/lib/utils'
import { getTranslations } from 'next-intl/server'
import { Button } from '@/components/ui/button'
import { AddToCartButton } from '@/components/cart/add-to-cart-button'
import { ArrowLeft } from 'lucide-react'
import Link from 'next/link'
import { ProductImageGallery } from '@/components/shop/product-image-gallery'
import { translateProduct, type Product } from '@/lib/product-translations'
import { type Locale } from '@/lib/supabase/database.types'

export const dynamic = 'force-dynamic'



interface ProductPageProps {
  params: Promise<{ locale: string; id: string }>
}

export default async function ProductPage({ params }: ProductPageProps) {
  const { locale, id } = await params
  const supabase = await createClient()

  const { data: product, error } = await supabase
    .from('products')
    .select('*')
    .eq('id', id)
    .eq('is_available', true)
    .single()

  if (error || !product) {
    notFound()
  }

  // Apply translations to the product
  const translatedProduct = translateProduct(product as Product, locale as Locale)

  const t = await getTranslations({ locale, namespace: 'shop.product' })
  const tc = await getTranslations({ locale, namespace: 'common' })

  const hasDiscount = !!(translatedProduct.discount_price && translatedProduct.discount_price < translatedProduct.price)
  const displayPrice = hasDiscount ? (translatedProduct.discount_price || translatedProduct.price) : translatedProduct.price

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        {/* Back */}
        <Button variant="ghost" size="sm" asChild className="mb-4">
          <Link href={`/${locale}/shop`}>
            <ArrowLeft className="mr-2 h-4 w-4" /> {tc('back')}
          </Link>
        </Button>

        <div className="grid md:grid-cols-2 gap-8 items-start">
          {/* Image Gallery */}
          <ProductImageGallery
            images={translatedProduct.images || []}
            title={translatedProduct.title}
            hasDiscount={hasDiscount}
            discountPercentage={hasDiscount ? Math.round(((translatedProduct.price - (translatedProduct.discount_price || 0)) / translatedProduct.price) * 100) : 0}
          />

          {/* Details */}
          <div className="space-y-6">
            <div>
              <h1 className="text-3xl font-bold mb-2">{translatedProduct.title}</h1>
              {translatedProduct.brand && <p className="text-muted-foreground mb-4">{translatedProduct.brand}</p>}
              <p className="whitespace-pre-line">{translatedProduct.description}</p>
            </div>

            {/* Attributes */}
            {translatedProduct.category === 'coffee' && (
              <div className="space-y-2 text-sm">
                {translatedProduct.coffee_type && (
                  <div className="flex justify-between"><span className="text-muted-foreground">{t('type')}:</span><span className="capitalize">{translatedProduct.coffee_type}</span></div>
                )}
                {translatedProduct.pack_quantity && translatedProduct.pack_quantity > 0 && (
                  <div className="flex justify-between"><span className="text-muted-foreground">{t('pack')}:</span><span>{translatedProduct.pack_quantity} {t('pieces')}</span></div>
                )}
                {translatedProduct.cost_per_espresso && translatedProduct.cost_per_espresso > 0 && (
                  <div className="flex justify-between"><span className="text-muted-foreground">{t('perEspresso')}:</span><span>{formatCurrency(translatedProduct.cost_per_espresso)}</span></div>
                )}
              </div>
            )}

            {/* Price */}
            <div className="flex items-center gap-3">
              <span className="text-3xl font-bold">{formatCurrency(displayPrice)}</span>
              {hasDiscount && (
                <span className="text-muted-foreground line-through">{formatCurrency(translatedProduct.price)}</span>
              )}
            </div>

            {/* Actions */}
            <div className="flex gap-3">
              <AddToCartButton productId={translatedProduct.id} size="lg" className="flex-1" />
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
