import { NextResponse } from 'next/server'
import { supabaseAdmin } from '@/lib/supabase/admin'

// Debug endpoint to check test orders
export async function GET() {
  try {
    console.log('🔍 Debug: Checking database connection and test orders...')
    console.log('🔍 Debug: NODE_ENV =', process.env.NODE_ENV)
    console.log('🔍 Debug: SUPABASE_URL =', process.env.NEXT_PUBLIC_SUPABASE_URL)
    console.log('🔍 Debug: SERVICE_ROLE_KEY =', process.env.SUPABASE_SERVICE_ROLE_KEY ? 'SET (length: ' + process.env.SUPABASE_SERVICE_ROLE_KEY.length + ')' : 'NOT SET')

    // Test user emails to check
    const testEmails = [
      '<EMAIL>',
      '<EMAIL>'
    ]

    // First, let's try to get ANY orders to test connection
    console.log('🔍 Debug: Testing basic connection with simple query...')
    const { data: allOrders, error: allOrdersError } = await supabaseAdmin
      .from('orders')
      .select('id, email, created_at')
      .limit(5)

    if (allOrdersError) {
      console.error('🔍 Debug: Error with basic query:', allOrdersError)
      return NextResponse.json({
        error: 'Database connection failed',
        details: allOrdersError,
        supabaseUrl: process.env.NEXT_PUBLIC_SUPABASE_URL,
        hasServiceKey: !!process.env.SUPABASE_SERVICE_ROLE_KEY
      }, { status: 500 })
    }

    console.log('🔍 Debug: Basic query successful, found', allOrders?.length || 0, 'orders')

    // Now try to find test orders
    console.log('🔍 Debug: Looking for test orders...')
    const { data: testOrders, error: testError } = await supabaseAdmin
      .from('orders')
      .select(`
        id,
        order_number,
        email,
        status,
        payment_status,
        created_at,
        total_amount
      `)
      .in('email', testEmails)
      .order('created_at', { ascending: false })

    if (testError) {
      console.error('🔍 Debug: Error fetching test orders:', testError)
      return NextResponse.json({
        error: 'Failed to fetch test orders',
        details: testError,
        basicQueryWorked: true,
        totalOrdersFound: allOrders?.length || 0
      }, { status: 500 })
    }

    console.log('🔍 Debug: Test orders query successful, found', testOrders?.length || 0, 'test orders')

    return NextResponse.json({
      success: true,
      message: 'Debug complete',
      environment: process.env.NODE_ENV,
      supabaseUrl: process.env.NEXT_PUBLIC_SUPABASE_URL,
      hasServiceKey: !!process.env.SUPABASE_SERVICE_ROLE_KEY,
      testEmails,
      totalOrdersInDb: allOrders?.length || 0,
      testOrdersFound: testOrders?.length || 0,
      allOrders: allOrders?.map(o => ({ id: o.id, email: o.email, created_at: o.created_at })) || [],
      testOrders: testOrders?.map(order => ({
        id: order.id,
        order_number: order.order_number,
        email: order.email,
        status: order.status,
        payment_status: order.payment_status,
        total_amount: order.total_amount,
        created_at: order.created_at,
        hours_since_creation: Math.floor(
          (new Date().getTime() - new Date(order.created_at).getTime()) / (1000 * 60 * 60)
        )
      })) || []
    })

  } catch (error) {
    console.error('🔍 Debug: Unexpected error:', error)
    return NextResponse.json({
      error: 'Unexpected error',
      details: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined
    }, { status: 500 })
  }
}
