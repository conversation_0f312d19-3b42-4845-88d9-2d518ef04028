-- Migration: Add product translations support
-- Created: 2025-07-18
-- Description: Add support for multilingual product titles and descriptions

-- Create product_translations table
CREATE TABLE IF NOT EXISTS product_translations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
    locale VARCHAR(2) NOT NULL CHECK (locale IN ('de', 'fr', 'it')),
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Ensure one translation per product per locale
    UNIQUE(product_id, locale)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_product_translations_product_id ON product_translations(product_id);
CREATE INDEX IF NOT EXISTS idx_product_translations_locale ON product_translations(locale);
CREATE INDEX IF NOT EXISTS idx_product_translations_product_locale ON product_translations(product_id, locale);

-- Add RLS policies for product_translations
ALTER TABLE product_translations ENABLE ROW LEVEL SECURITY;

-- Policy: Allow authenticated users to read all translations
CREATE POLICY "Allow authenticated users to read product translations" ON product_translations
    FOR SELECT TO authenticated
    USING (true);

-- Policy: Allow admin users to manage translations
CREATE POLICY "Allow admin users to manage product translations" ON product_translations
    FOR ALL TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE users.id = auth.uid() 
            AND users.is_admin = true
        )
    );

-- Function to get product with translations
CREATE OR REPLACE FUNCTION get_product_with_translations(product_uuid UUID, user_locale VARCHAR(2) DEFAULT 'de')
RETURNS TABLE (
    id UUID,
    title TEXT,
    description TEXT,
    slug TEXT,
    category TEXT,
    coffee_type TEXT,
    brand TEXT,
    blend TEXT,
    machine_compatibility TEXT[],
    pack_quantity INTEGER,
    pack_weight_grams INTEGER,
    price DECIMAL(10,2),
    discount_price DECIMAL(10,2),
    cost_per_espresso DECIMAL(10,4),
    images TEXT[],
    inventory_count INTEGER,
    purchase_cost DECIMAL(10,2),
    is_available BOOLEAN,
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ,
    title_template TEXT,
    template_id UUID
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        p.id,
        COALESCE(pt.title, p.title) as title,
        COALESCE(pt.description, p.description) as description,
        p.slug,
        p.category::TEXT,
        p.coffee_type::TEXT,
        p.brand,
        p.blend,
        p.machine_compatibility,
        p.pack_quantity,
        p.pack_weight_grams,
        p.price,
        p.discount_price,
        p.cost_per_espresso,
        p.images,
        p.inventory_count,
        p.purchase_cost,
        p.is_available,
        p.created_at,
        p.updated_at,
        p.title_template,
        p.template_id
    FROM products p
    LEFT JOIN product_translations pt ON p.id = pt.product_id AND pt.locale = user_locale
    WHERE p.id = product_uuid;
END;
$$ LANGUAGE plpgsql;

-- Function to get all products with translations for a specific locale
CREATE OR REPLACE FUNCTION get_products_with_translations(user_locale VARCHAR(2) DEFAULT 'de')
RETURNS TABLE (
    id UUID,
    title TEXT,
    description TEXT,
    slug TEXT,
    category TEXT,
    coffee_type TEXT,
    brand TEXT,
    blend TEXT,
    machine_compatibility TEXT[],
    pack_quantity INTEGER,
    pack_weight_grams INTEGER,
    price DECIMAL(10,2),
    discount_price DECIMAL(10,2),
    cost_per_espresso DECIMAL(10,4),
    images TEXT[],
    inventory_count INTEGER,
    purchase_cost DECIMAL(10,2),
    is_available BOOLEAN,
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ,
    title_template TEXT,
    template_id UUID
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        p.id,
        COALESCE(pt.title, p.title) as title,
        COALESCE(pt.description, p.description) as description,
        p.slug,
        p.category::TEXT,
        p.coffee_type::TEXT,
        p.brand,
        p.blend,
        p.machine_compatibility,
        p.pack_quantity,
        p.pack_weight_grams,
        p.price,
        p.discount_price,
        p.cost_per_espresso,
        p.images,
        p.inventory_count,
        p.purchase_cost,
        p.is_available,
        p.created_at,
        p.updated_at,
        p.title_template,
        p.template_id
    FROM products p
    LEFT JOIN product_translations pt ON p.id = pt.product_id AND pt.locale = user_locale
    ORDER BY p.created_at DESC;
END;
$$ LANGUAGE plpgsql;

-- Function to update product translations
CREATE OR REPLACE FUNCTION upsert_product_translation(
    product_uuid UUID,
    translation_locale VARCHAR(2),
    translation_title TEXT,
    translation_description TEXT
)
RETURNS UUID AS $$
DECLARE
    translation_id UUID;
BEGIN
    -- Insert or update translation
    INSERT INTO product_translations (product_id, locale, title, description)
    VALUES (product_uuid, translation_locale, translation_title, translation_description)
    ON CONFLICT (product_id, locale)
    DO UPDATE SET
        title = EXCLUDED.title,
        description = EXCLUDED.description,
        updated_at = NOW()
    RETURNING id INTO translation_id;
    
    RETURN translation_id;
END;
$$ LANGUAGE plpgsql;

-- Trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_product_translations_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_product_translations_updated_at
    BEFORE UPDATE ON product_translations
    FOR EACH ROW
    EXECUTE FUNCTION update_product_translations_updated_at();
