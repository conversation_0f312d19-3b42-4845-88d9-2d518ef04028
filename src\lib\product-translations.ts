import { type Locale } from '@/lib/supabase/database.types'

export interface Product {
  id: string
  title: string
  description: string
  slug?: string
  category: string
  coffee_type: string | null
  brand: string | null
  blend: string | null
  machine_compatibility: string[] | null
  pack_quantity: number | null
  pack_weight_grams: number | null
  price: number
  discount_price: number | null
  cost_per_espresso: number | null
  images: string[]
  inventory_count?: number
  purchase_cost?: number | null
  is_available: boolean
  created_at?: string
  updated_at?: string
  translations?: {
    [key: string]: {
      title: string
      description: string
    }
  } | null
}



/**
 * Get translated title and description for a product based on locale
 */
export function getProductTranslation(
  product: Product,
  locale: Locale
): { title: string; description: string } {
  // Check if translations exist and if there's a translation for the current locale
  if (
    product.translations &&
    typeof product.translations === 'object' &&
    product.translations[locale] &&
    product.translations[locale].title &&
    product.translations[locale].description
  ) {
    return {
      title: product.translations[locale].title,
      description: product.translations[locale].description
    }
  }

  // Fallback to original title and description
  return {
    title: product.title,
    description: product.description
  }
}

/**
 * Transform a product to use translated title and description
 */
export function translateProduct(product: Product, locale: Locale): Product {
  const translation = getProductTranslation(product, locale)

  return {
    ...product,
    title: translation.title,
    description: translation.description
  }
}

/**
 * Transform an array of products to use translated titles and descriptions
 * Returns products with the same structure but translated content
 */
export function translateProducts(products: Product[], locale: Locale): Product[] {
  return products.map(product => {
    const translation = getProductTranslation(product, locale)

    return {
      ...product,
      title: translation.title,
      description: translation.description
    }
  })
}
