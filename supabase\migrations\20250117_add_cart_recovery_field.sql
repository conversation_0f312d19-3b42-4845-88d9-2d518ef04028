-- Migration to add cart_recovery_sent_at field to orders table
-- This field tracks when a cart recovery email was sent for abandoned orders

-- Add cart_recovery_sent_at column to orders table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'orders' 
        AND column_name = 'cart_recovery_sent_at'
    ) THEN
        ALTER TABLE orders ADD COLUMN cart_recovery_sent_at TIMESTAMPTZ;
        
        -- Add index for efficient querying of orders without recovery emails
        CREATE INDEX idx_orders_cart_recovery_sent_at ON orders(cart_recovery_sent_at) WHERE cart_recovery_sent_at IS NULL;
        
        -- Add comment to document the field
        COMMENT ON COLUMN orders.cart_recovery_sent_at IS 'Timestamp when cart recovery email was sent for this abandoned order';
        
        RAISE NOTICE 'Added cart_recovery_sent_at column to orders table';
    ELSE
        RAISE NOTICE 'cart_recovery_sent_at column already exists in orders table';
    END IF;
END $$;
