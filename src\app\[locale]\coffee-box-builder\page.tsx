import { createClient } from '@/lib/supabase/server'
import { getTranslations } from 'next-intl/server';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Coffee, Package, Gift, Truck, Info } from 'lucide-react'
import { CoffeeBoxBuilder } from '@/components/coffee-box-builder/coffee-box-builder'
import { translateProducts, type Product } from '@/lib/product-translations'
import { type Locale } from '@/lib/supabase/database.types'

export const dynamic = 'force-dynamic';

interface CoffeeBoxBuilderPageProps {
  params: Promise<{ locale: string }>;
}

export default async function CoffeeBoxBuilderPage({ params }: CoffeeBoxBuilderPageProps) {
  const { locale } = await params;
  const supabase = await createClient()

  // Get initial 20 products for SSR and filter options
  const { data: coffeeProducts, error } = await supabase
    .from('products')
    .select('*')
    .eq('is_available', true)
    .order('title')
    .limit(20)

  if (error) {
    console.error('Error fetching coffee products:', error)
  }

  // Apply translations to initial products
  const translatedProducts = translateProducts(coffeeProducts as Product[] || [], locale as Locale)
  const t = await getTranslations({ locale, namespace: 'coffeeBoxBuilder' });

  return (
    <div className="container mx-auto px-4 py-8">
      <CoffeeBoxBuilderContent initialProducts={translatedProducts} t={t} />
    </div>
  )
}



function CoffeeBoxBuilderContent({ initialProducts, t }: { initialProducts: Product[], t: Awaited<ReturnType<typeof getTranslations>> }) {

  return (
    <>
      {/* Header */}
      <div className="text-center mb-12 animate-fade-in-up">
        <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-primary/20 to-primary/10 rounded-full mb-6 shadow-soft hover:shadow-glow transition-all-smooth animate-float">
          <Package className="h-8 w-8 text-primary" />
        </div>
        <h1 className="text-4xl lg:text-5xl font-bold mb-4 bg-gradient-to-r from-foreground via-foreground/90 to-foreground/80 bg-clip-text text-transparent">
          {t('title')}
        </h1>
        <p className="text-xl text-muted-foreground max-w-3xl mx-auto mb-8 animate-fade-in-up" style={{animationDelay: '0.2s'}}>
          {t('description')}
        </p>
        
        {/* Benefits */}
        <div className="grid md:grid-cols-3 gap-6 max-w-4xl mx-auto">
          <Card className="text-center">
            <CardContent className="pt-6">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <Coffee className="h-6 w-6 text-blue-600" />
              </div>
              <h3 className="font-semibold mb-2">{t('features.premium.title')}</h3>
              <p className="text-sm text-muted-foreground">
                {t('features.premium.description')}
              </p>
            </CardContent>
          </Card>
          
          <Card className="text-center">
            <CardContent className="pt-6">
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <Gift className="h-6 w-6 text-green-600" />
              </div>
              <h3 className="font-semibold mb-2">{t('features.convenient.title')}</h3>
              <p className="text-sm text-muted-foreground">
                {t('features.convenient.description')}
              </p>
            </CardContent>
          </Card>
          
          <Card className="text-center">
            <CardContent className="pt-6">
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <Truck className="h-6 w-6 text-purple-600" />
              </div>
              <h3 className="font-semibold mb-2">{t('features.shipping.title')}</h3>
              <p className="text-sm text-muted-foreground">
                {t('features.shipping.description')}
              </p>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Instructions */}
      <Card className="mb-8 border-blue-200 bg-blue-50/50">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-blue-800">
            <Info className="h-5 w-5" />
            {t('howItWorks.title')}
          </CardTitle>
        </CardHeader>
        <CardContent className="text-blue-700">
          <div className="grid md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-semibold mb-2">1. {t('howItWorks.step1.title')}</h4>
              <p className="text-sm">
                {t('howItWorks.step1.description')}
              </p>
            </div>
            <div>
              <h4 className="font-semibold mb-2">2. {t('howItWorks.step2.title')}</h4>
              <p className="text-sm">
                {t('howItWorks.step2.description')}
              </p>
            </div>
            <div>
              <h4 className="font-semibold mb-2">3. {t('howItWorks.step3.title')}</h4>
              <p className="text-sm">
                {t('howItWorks.step3.description')}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Coffee Box Builder Component */}
      <CoffeeBoxBuilder initialProducts={initialProducts} />
    </>
  )
}
