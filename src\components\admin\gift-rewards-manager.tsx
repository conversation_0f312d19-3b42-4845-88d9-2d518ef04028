'use client'

import { useState, useEffect, useCallback } from 'react'
import { createClient } from '@/lib/supabase/client'
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from '@/components/ui/select'

import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { useToast } from '@/hooks/use-toast'
import { Trophy, Star, Save } from 'lucide-react'

interface Product {
  id: string
  title: string
}

interface GiftReward {
  id: string
  type: 'level' | 'league'
  trigger_value: number
  option_1_product_id: string | null
  option_2_product_id: string | null
  option_3_product_id: string | null
  is_active: boolean
  option_1_product?: Product
  option_2_product?: Product
  option_3_product?: Product
}

interface LeagueConfig {
  levels_per_league: number
}

export default function GiftRewardsManager() {
  const supabase = createClient()
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState<string | null>(null)
  const [products, setProducts] = useState<Product[]>([])
  const [rewards, setRewards] = useState<GiftReward[]>([])
  const [selectedLeague, setSelectedLeague] = useState<number>(1)
  const [leagueConfig, setLeagueConfig] = useState<LeagueConfig | null>(null)
  const { toast } = useToast()

  const loadData = useCallback(async () => {
    setLoading(true)
    try {
      // Load league configuration
      const { data: leagueData } = await supabase
        .from('league_config')
        .select('levels_per_league')
        .eq('is_active', true)
        .single()

      if (leagueData) {
        setLeagueConfig(leagueData)
      }

      // Load products
      const { data: productsData } = await supabase
        .from('products')
        .select('id, title')
        .eq('is_available', true)
        .order('title')

      console.log('Products loaded:', productsData?.length)

      // Load gift rewards
      const response = await fetch('/api/admin/gamification/gifts')
      const data = await response.json()

      console.log('Gift rewards response:', data)

      setProducts(productsData || [])
      setRewards(data.rewards || [])
    } catch (error) {
      console.error('Error loading data:', error)
      toast({ title: 'Error', description: 'Failed to load data', variant: 'destructive' })
    } finally {
      setLoading(false)
    }
  }, [supabase, toast])

  useEffect(() => {
    loadData()
  }, [loadData])

  const updateReward = async (rewardId: string, updates: Partial<GiftReward>) => {
    setSaving(rewardId)
    try {
      const response = await fetch('/api/admin/gamification/gifts', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ id: rewardId, ...updates }),
      })

      if (response.ok) {
        toast({ title: 'Success', description: 'Gift reward updated successfully' })
        await loadData()
      } else {
        throw new Error('Failed to update')
      }
    } catch (error) {
      console.error('Error updating reward:', error)
      toast({ title: 'Error', description: 'Failed to update gift reward', variant: 'destructive' })
    } finally {
      setSaving(null)
    }
  }

  // Get league rewards
  const leagueRewards = rewards.filter(r => r.type === 'league')

  // Get level rewards for selected league (levels 1-9, excluding level 10)
  const getSelectedLeagueLevels = () => {
    const levelsPerLeague = leagueConfig?.levels_per_league || 10
    const startLevel = (selectedLeague - 1) * levelsPerLeague + 1
    const endLevel = selectedLeague * levelsPerLeague - 1 // Exclude last level (e.g., for league 1: levels 1-9)
    return rewards.filter(r =>
      r.type === 'level' &&
      r.trigger_value >= startLevel &&
      r.trigger_value <= endLevel
    )
  }

  const selectedLeagueLevels = getSelectedLeagueLevels()

  // Generate leagues dynamically based on configuration
  const leagues = leagueConfig ? [
    {
      value: 1,
      label: `🥉 Bronze (Livelli 1-${leagueConfig.levels_per_league - 1})`,
      range: `1-${leagueConfig.levels_per_league - 1}`
    },
    {
      value: 2,
      label: `🥈 Silver (Livelli ${leagueConfig.levels_per_league + 1}-${leagueConfig.levels_per_league * 2 - 1})`,
      range: `${leagueConfig.levels_per_league + 1}-${leagueConfig.levels_per_league * 2 - 1}`
    },
    {
      value: 3,
      label: `🥇 Gold (Livelli ${leagueConfig.levels_per_league * 2 + 1}-${leagueConfig.levels_per_league * 3 - 1})`,
      range: `${leagueConfig.levels_per_league * 2 + 1}-${leagueConfig.levels_per_league * 3 - 1}`
    },
    {
      value: 4,
      label: `💎 Platinum (Livelli ${leagueConfig.levels_per_league * 3 + 1}-${leagueConfig.levels_per_league * 4 - 1})`,
      range: `${leagueConfig.levels_per_league * 3 + 1}-${leagueConfig.levels_per_league * 4 - 1}`
    },
    {
      value: 5,
      label: `💠 Diamond (Livelli ${leagueConfig.levels_per_league * 4 + 1}-${leagueConfig.levels_per_league * 5 - 1})`,
      range: `${leagueConfig.levels_per_league * 4 + 1}-${leagueConfig.levels_per_league * 5 - 1}`
    }
  ] : [
    // Fallback with default values
    { value: 1, label: '🥉 Bronze (Livelli 1-9)', range: '1-9' },
    { value: 2, label: '🥈 Silver (Livelli 11-19)', range: '11-19' },
    { value: 3, label: '🥇 Gold (Livelli 21-29)', range: '21-29' },
    { value: 4, label: '💎 Platinum (Livelli 31-39)', range: '31-39' },
    { value: 5, label: '💠 Diamond (Livelli 41-49)', range: '41-49' }
  ]

  if (loading) {
    return <div className="p-6">Loading gift rewards...</div>
  }

  if (rewards.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Gift Rewards</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <p className="text-muted-foreground mb-4">
              No gift rewards found. Make sure the database has been initialized with gift rewards.
            </p>
            <p className="text-sm text-muted-foreground">
              Products available: {products.length}
            </p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Instructions */}
      <Card className="bg-blue-50 border-blue-200">
        <CardHeader>
          <CardTitle className="text-blue-900">🎁 Come configurare i regali</CardTitle>
        </CardHeader>
        <CardContent className="text-blue-800">
          <div className="space-y-2 text-sm">
            {leagueConfig ? (
              <>
                <p><strong>1. Regali di Lega (🏆):</strong> Si attivano ogni {leagueConfig.levels_per_league} livelli (livello {leagueConfig.levels_per_league}, {leagueConfig.levels_per_league * 2}, {leagueConfig.levels_per_league * 3}, {leagueConfig.levels_per_league * 4}, {leagueConfig.levels_per_league * 5})</p>
                <p><strong>2. Regali di Livello (⭐):</strong> Si attivano sui livelli 1-{leagueConfig.levels_per_league - 1}, {leagueConfig.levels_per_league + 1}-{leagueConfig.levels_per_league * 2 - 1}, {leagueConfig.levels_per_league * 2 + 1}-{leagueConfig.levels_per_league * 3 - 1}, {leagueConfig.levels_per_league * 3 + 1}-{leagueConfig.levels_per_league * 4 - 1}, {leagueConfig.levels_per_league * 4 + 1}-{leagueConfig.levels_per_league * 5 - 1}</p>
                <p><strong>3. Priorità:</strong> I livelli {leagueConfig.levels_per_league}, {leagueConfig.levels_per_league * 2}, {leagueConfig.levels_per_league * 3}, {leagueConfig.levels_per_league * 4}, {leagueConfig.levels_per_league * 5} danno SOLO il regalo della lega</p>
              </>
            ) : (
              <>
                <p><strong>1. Regali di Lega (🏆):</strong> Si attivano ogni 10 livelli (livello 10, 20, 30, 40, 50)</p>
                <p><strong>2. Regali di Livello (⭐):</strong> Si attivano sui livelli 1-9, 11-19, 21-29, 31-39, 41-49</p>
                <p><strong>3. Priorità:</strong> I livelli 10, 20, 30, 40, 50 danno SOLO il regalo della lega</p>
              </>
            )}
            <p><strong>4. Configurazione:</strong> Per ogni regalo, scegli 3 prodotti. L&apos;utente potrà scegliere quale preferisce.</p>
          </div>
        </CardContent>
      </Card>

      {/* League Rewards */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Trophy className="h-5 w-5" />
            🏆 Regali di Lega (Priorità Alta)
          </CardTitle>
          <p className="text-sm text-muted-foreground">
            Configura i regali per le leghe. Questi hanno priorità sui regali di livello.
          </p>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4">
            {leagueRewards.map((reward) => (
              <RewardRow
                key={reward.id}
                reward={reward}
                products={products}
                onUpdate={updateReward}
                saving={saving === reward.id}
                icon={<Trophy className="h-4 w-4" />}
                label={`🏆 Lega ${reward.trigger_value} (Livello ${reward.trigger_value * 10})`}
              />
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Level Rewards */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Star className="h-5 w-5" />
            ⭐ Regali di Livello
          </CardTitle>
          <p className="text-sm text-muted-foreground">
            Configura i regali per i singoli livelli. I livelli 10, 20, 30, 40, 50 sono gestiti dai regali di lega.
          </p>

          {/* League Selector */}
          <div className="mt-4">
            <Label htmlFor="league-select">Seleziona Lega:</Label>
            <Select value={selectedLeague.toString()} onValueChange={(value) => setSelectedLeague(parseInt(value))}>
              <SelectTrigger className="max-w-xs">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {leagues.map((league) => (
                  <SelectItem key={league.value} value={league.value.toString()}>
                    {league.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardHeader>
        <CardContent>
          {selectedLeagueLevels.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground">
                Nessun livello trovato per la lega {leagues.find(l => l.value === selectedLeague)?.label}
              </p>
              <p className="text-sm text-muted-foreground mt-2">
                Assicurati che i livelli {leagues.find(l => l.value === selectedLeague)?.range} siano stati creati nel database.
              </p>
            </div>
          ) : (
            <div className="grid gap-4">
              {selectedLeagueLevels.map((reward) => (
                <RewardRow
                  key={reward.id}
                  reward={reward}
                  products={products}
                  onUpdate={updateReward}
                  saving={saving === reward.id}
                  icon={<Star className="h-4 w-4" />}
                  label={`⭐ Livello ${reward.trigger_value}`}
                />
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

interface RewardRowProps {
  reward: GiftReward
  products: Product[]
  onUpdate: (id: string, updates: Partial<GiftReward>) => void
  saving: boolean
  icon: React.ReactNode
  label: string
}

function RewardRow({ reward, products, onUpdate, saving, icon, label }: RewardRowProps) {
  const [option1, setOption1] = useState(reward.option_1_product_id || undefined)
  const [option2, setOption2] = useState(reward.option_2_product_id || undefined)
  const [option3, setOption3] = useState(reward.option_3_product_id || undefined)

  const hasChanges =
    option1 !== (reward.option_1_product_id || undefined) ||
    option2 !== (reward.option_2_product_id || undefined) ||
    option3 !== (reward.option_3_product_id || undefined)

  const isConfigured = option1 || option2 || option3

  const handleSave = () => {
    onUpdate(reward.id, {
      option_1_product_id: option1 || null,
      option_2_product_id: option2 || null,
      option_3_product_id: option3 || null,
    })
  }

  return (
    <div className={`border rounded-lg p-4 space-y-4 ${isConfigured ? 'border-green-200 bg-green-50' : 'border-orange-200 bg-orange-50'}`}>
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          {icon}
          <span className="font-medium">{label}</span>
          <Badge variant={isConfigured ? 'default' : 'secondary'}>
            {isConfigured ? '✅ Configurato' : '⚠️ Da configurare'}
          </Badge>
        </div>
        <Button
          onClick={handleSave}
          disabled={!hasChanges || saving}
          size="sm"
          className="flex items-center gap-2"
          variant={hasChanges ? 'default' : 'outline'}
        >
          <Save className="h-4 w-4" />
          {saving ? 'Salvando...' : 'Salva'}
        </Button>
      </div>

      <div className="space-y-3">
        <p className="text-sm text-muted-foreground">
          Scegli 3 prodotti tra cui l&apos;utente potrà scegliere:
        </p>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <Label>🎁 Opzione 1</Label>
            <Select value={option1 || "none"} onValueChange={(value) => setOption1(value === "none" ? undefined : value)}>
              <SelectTrigger>
                <SelectValue placeholder="Seleziona prodotto..." />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="none">Nessun prodotto</SelectItem>
              {products.map((product) => (
                <SelectItem key={product.id} value={product.id}>
                  {product.title}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

          <div>
            <Label>🎁 Opzione 2</Label>
            <Select value={option2 || "none"} onValueChange={(value) => setOption2(value === "none" ? undefined : value)}>
              <SelectTrigger>
                <SelectValue placeholder="Seleziona prodotto..." />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="none">Nessun prodotto</SelectItem>
              {products.map((product) => (
                <SelectItem key={product.id} value={product.id}>
                  {product.title}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

          <div>
            <Label>🎁 Opzione 3</Label>
            <Select value={option3 || "none"} onValueChange={(value) => setOption3(value === "none" ? undefined : value)}>
              <SelectTrigger>
                <SelectValue placeholder="Seleziona prodotto..." />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="none">Nessun prodotto</SelectItem>
              {products.map((product) => (
                <SelectItem key={product.id} value={product.id}>
                  {product.title}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          </div>
        </div>
      </div>
    </div>
  )
}
