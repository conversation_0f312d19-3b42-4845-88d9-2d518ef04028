import { NextRequest, NextResponse } from 'next/server'
import { supabaseAdmin } from '@/lib/supabase/admin'
import { sendCartRecoveryEmail } from '@/lib/email'

// This endpoint can be called by external cron services like Vercel Cron
export async function GET(request: NextRequest) {
  try {
    // Verify the request is from a trusted source (optional security measure)
    const authHeader = request.headers.get('authorization')
    const cronSecret = process.env.CRON_SECRET

    if (cronSecret && authHeader !== `Bearer ${cronSecret}`) {
      console.log('📧 Cart Recovery Cron: Unauthorized recovery attempt')
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    console.log('📧 Cart Recovery Cron: Starting cart recovery email process...')

    // Get orders that are pending for exactly 2 hours (with some tolerance)
    const twoHoursAgo = new Date()
    twoHoursAgo.setHours(twoHoursAgo.getHours() - 2)
    
    // Add 10 minutes tolerance to catch orders that might have been missed
    const twoHoursAndTenMinutesAgo = new Date()
    twoHoursAndTenMinutesAgo.setHours(twoHoursAndTenMinutesAgo.getHours() - 2)
    twoHoursAndTenMinutesAgo.setMinutes(twoHoursAndTenMinutesAgo.getMinutes() - 10)

    // Find pending orders that are 2 hours old and haven't received recovery email yet
    const { data: pendingOrders, error: fetchError } = await supabaseAdmin
      .from('orders')
      .select(`
        id,
        user_id,
        email,
        status,
        payment_status,
        created_at,
        total_amount,
        order_number,
        cart_recovery_sent_at
      `)
      .eq('status', 'pending')
      .eq('payment_status', 'pending')
      .is('cart_recovery_sent_at', null) // Only orders that haven't received recovery email
      .gte('created_at', twoHoursAndTenMinutesAgo.toISOString())
      .lte('created_at', twoHoursAgo.toISOString())

    if (fetchError) {
      console.error('📧 Cart Recovery Cron: Error fetching pending orders:', fetchError)
      return NextResponse.json(
        { error: 'Failed to fetch pending orders' },
        { status: 500 }
      )
    }

    if (!pendingOrders || pendingOrders.length === 0) {
      console.log('📧 Cart Recovery Cron: No pending orders found for recovery')
      return NextResponse.json({
        success: true,
        message: 'No pending orders found for recovery',
        emailsSent: 0,
        timestamp: new Date().toISOString()
      })
    }

    console.log(`📧 Cart Recovery Cron: Found ${pendingOrders.length} orders for cart recovery`)

    let emailsSent = 0
    const emailErrors: string[] = []

    // Process each order
    for (const order of pendingOrders) {
      try {
        console.log(`📧 Cart Recovery Cron: Processing order ${order.id} for ${order.email}`)

        // Get user's preferred language
        let userLocale = 'de' // Default fallback
        if (order.user_id) {
          try {
            const { data: userData } = await supabaseAdmin
              .from('users')
              .select('preferred_language')
              .eq('id', order.user_id)
              .single()

            if (userData?.preferred_language) {
              userLocale = userData.preferred_language
            }
          } catch {
            console.log(`📧 Cart Recovery Cron: Could not fetch user language for ${order.email}, using default:`, userLocale)
          }
        } else {
          // For guest orders, try to get site default language
          try {
            const { data: settings } = await supabaseAdmin
              .from('site_settings')
              .select('default_language')
              .single()

            if (settings?.default_language) {
              userLocale = settings.default_language
            }
          } catch {
            console.log(`📧 Cart Recovery Cron: Could not fetch site settings for ${order.email}, using default locale:`, userLocale)
          }
        }

        console.log(`📧 Cart Recovery Cron: Sending recovery email to ${order.email} in locale: ${userLocale}`)

        // Send cart recovery email
        await sendCartRecoveryEmail(order, userLocale)

        // Mark order as having received recovery email
        const { error: updateError } = await supabaseAdmin
          .from('orders')
          .update({
            cart_recovery_sent_at: new Date().toISOString()
          })
          .eq('id', order.id)

        if (updateError) {
          console.error(`📧 Cart Recovery Cron: Error updating order ${order.id}:`, updateError)
          emailErrors.push(`Failed to update order ${order.id}: ${updateError.message}`)
        } else {
          emailsSent++
          console.log(`📧 Cart Recovery Cron: Successfully sent recovery email for order ${order.id}`)
        }

      } catch (emailError) {
        const errorMessage = `Failed to send recovery email for order ${order.id} (${order.email}): ${emailError instanceof Error ? emailError.message : 'Unknown error'}`
        console.error(`📧 Cart Recovery Cron: ${errorMessage}`)
        emailErrors.push(errorMessage)
      }
    }

    const result = {
      success: true,
      message: `Cart recovery process completed. Sent ${emailsSent} emails`,
      emailsSent,
      totalOrdersProcessed: pendingOrders.length,
      emailErrors: emailErrors.length > 0 ? emailErrors : undefined,
      timestamp: new Date().toISOString(),
      processedOrders: pendingOrders.map(order => ({
        id: order.id,
        email: order.email,
        total_amount: order.total_amount,
        created_at: order.created_at,
        hours_since_creation: Math.floor(
          (new Date().getTime() - new Date(order.created_at).getTime()) / (1000 * 60 * 60)
        )
      }))
    }

    console.log(`📧 Cart Recovery Cron: Process completed successfully. Sent ${emailsSent} recovery emails`)

    return NextResponse.json(result)

  } catch (error) {
    console.error('📧 Cart Recovery Cron: Error in cart recovery process:', error)
    return NextResponse.json(
      { 
        error: 'Internal server error',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    )
  }
}

// POST method for manual triggering (same logic as GET)
export async function POST(request: NextRequest) {
  return GET(request)
}
