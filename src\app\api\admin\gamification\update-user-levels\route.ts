import { NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { updateAllUserLevels } from '@/lib/gamification'

// POST - Update all users to their correct level based on current points
export async function POST() {
  try {
    const supabase = await createClient()

    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { data: profile } = await supabase
      .from('users')
      .select('is_admin')
      .eq('id', user.id)
      .single()

    if (!profile?.is_admin) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    // Update all user levels
    const result = await updateAllUserLevels()

    return NextResponse.json({ 
      success: true, 
      message: `Successfully updated ${result.usersUpdated} out of ${result.totalUsers} users to correct levels`,
      usersUpdated: result.usersUpdated,
      totalUsers: result.totalUsers
    })
  } catch (error) {
    console.error('Error updating user levels:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
