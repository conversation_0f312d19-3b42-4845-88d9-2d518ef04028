'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { useToast } from '@/hooks/use-toast'

import { Trophy, Zap, Save, RotateCcw, RefreshCw, Users } from 'lucide-react'
import { LeagueConfig } from '@/lib/gamification'

interface LeagueManagementProps {
  onConfigUpdated?: () => void
}

export default function LeagueManagement({ onConfigUpdated }: LeagueManagementProps) {
  const { toast } = useToast()

  const [loading, setLoading] = useState(false)
  const [regenerating, setRegenerating] = useState(false)
  const [updatingUsers, setUpdatingUsers] = useState(false)
  const [config, setConfig] = useState<LeagueConfig>({
    id: '',
    levels_per_league: 10,
    league_1_points: 0,
    league_2_points: 1000,
    league_3_points: 3000,
    league_4_points: 6000,
    league_5_points: 10000,
    league_1_discount: 0,
    league_2_discount: 5,
    league_3_discount: 10,
    league_4_discount: 15,
    league_5_discount: 20,
    global_multiplier: 1.0,
    multiplier_description: 'Standard rate',
    is_active: true
  })

  useEffect(() => {
    loadConfig()
  }, [])

  const loadConfig = async () => {
    try {
      const response = await fetch('/api/admin/gamification/league-config')
      if (response.ok) {
        const data = await response.json()
        if (data.config) {
          setConfig(data.config)
        }
      }
    } catch (error) {
      console.error('Error loading league config:', error)
    }
  }

  const saveConfig = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/admin/gamification/league-config', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(config)
      })

      if (response.ok) {
        toast({
          title: 'League Configuration Saved',
          description: 'League discounts and multiplier settings have been updated successfully.'
        })
        onConfigUpdated?.()
      } else {
        const error = await response.json()
        toast({
          title: 'Error Saving Configuration',
          description: error.error || 'Failed to save league configuration',
          variant: 'destructive'
        })
      }
    } catch (error) {
      console.error('Error saving config:', error)
      toast({
        title: 'Error Saving Configuration',
        description: 'An unexpected error occurred while saving the configuration',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  const regenerateLevels = async () => {
    setRegenerating(true)
    try {
      const response = await fetch('/api/admin/gamification/regenerate-levels', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      })

      if (response.ok) {
        toast({
          title: 'Levels Regenerated',
          description: 'All levels have been regenerated based on current configuration.'
        })
        onConfigUpdated?.()
      } else {
        const error = await response.json()
        toast({
          title: 'Error Regenerating Levels',
          description: error.error || 'Failed to regenerate levels',
          variant: 'destructive'
        })
      }
    } catch (error) {
      console.error('Error regenerating levels:', error)
      toast({
        title: 'Error Regenerating Levels',
        description: 'An unexpected error occurred while regenerating levels',
        variant: 'destructive'
      })
    } finally {
      setRegenerating(false)
    }
  }

  const updateUserLevels = async () => {
    setUpdatingUsers(true)
    try {
      const response = await fetch('/api/admin/gamification/update-user-levels', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      })

      if (response.ok) {
        const result = await response.json()
        toast({
          title: 'User Levels Updated',
          description: `Successfully updated ${result.usersUpdated} out of ${result.totalUsers} users to correct levels.`
        })
        onConfigUpdated?.()
      } else {
        const error = await response.json()
        toast({
          title: 'Error Updating User Levels',
          description: error.error || 'Failed to update user levels',
          variant: 'destructive'
        })
      }
    } catch (error) {
      console.error('Error updating user levels:', error)
      toast({
        title: 'Error Updating User Levels',
        description: 'An unexpected error occurred while updating user levels',
        variant: 'destructive'
      })
    } finally {
      setUpdatingUsers(false)
    }
  }

  const resetToDefaults = () => {
    setConfig({
      ...config,
      levels_per_league: 10,
      league_1_points: 0,
      league_2_points: 1000,
      league_3_points: 3000,
      league_4_points: 6000,
      league_5_points: 10000,
      league_1_discount: 0,
      league_2_discount: 5,
      league_3_discount: 10,
      league_4_discount: 15,
      league_5_discount: 20,
      global_multiplier: 1.0,
      multiplier_description: 'Standard rate'
    })
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Trophy className="h-5 w-5" />
            League Management
          </CardTitle>
          <p className="text-sm text-muted-foreground">
            Configure league points thresholds, levels per league, and discounts. The system automatically generates individual levels based on your configuration.
          </p>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* League Configuration */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">League Configuration</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="levels_per_league">Levels per League</Label>
                <Input
                  id="levels_per_league"
                  type="number"
                  min="5"
                  max="20"
                  value={config.levels_per_league}
                  onChange={(e) => setConfig({ ...config, levels_per_league: parseInt(e.target.value) || 10 })}
                  className="mt-1"
                />
                <p className="text-xs text-muted-foreground mt-1">
                  Number of levels in each league (5-20)
                </p>
              </div>
            </div>
          </div>

          {/* League Points Thresholds */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">League Points Thresholds</h3>
            <p className="text-sm text-muted-foreground">
              Set the minimum points required to reach each league. The system will automatically distribute levels within each league.
            </p>
            <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
              <div>
                <Label htmlFor="league_1_points">Bronze League</Label>
                <Input
                  id="league_1_points"
                  type="number"
                  min="0"
                  value={config.league_1_points}
                  onChange={(e) => setConfig({ ...config, league_1_points: parseInt(e.target.value) || 0 })}
                  className="mt-1"
                />
                <p className="text-xs text-muted-foreground mt-1">Starting points</p>
              </div>

              <div>
                <Label htmlFor="league_2_points">Silver League</Label>
                <Input
                  id="league_2_points"
                  type="number"
                  min="0"
                  value={config.league_2_points}
                  onChange={(e) => setConfig({ ...config, league_2_points: parseInt(e.target.value) || 0 })}
                  className="mt-1"
                />
                <p className="text-xs text-muted-foreground mt-1">Min points to reach</p>
              </div>

              <div>
                <Label htmlFor="league_3_points">Gold League</Label>
                <Input
                  id="league_3_points"
                  type="number"
                  min="0"
                  value={config.league_3_points}
                  onChange={(e) => setConfig({ ...config, league_3_points: parseInt(e.target.value) || 0 })}
                  className="mt-1"
                />
                <p className="text-xs text-muted-foreground mt-1">Min points to reach</p>
              </div>

              <div>
                <Label htmlFor="league_4_points">Platinum League</Label>
                <Input
                  id="league_4_points"
                  type="number"
                  min="0"
                  value={config.league_4_points}
                  onChange={(e) => setConfig({ ...config, league_4_points: parseInt(e.target.value) || 0 })}
                  className="mt-1"
                />
                <p className="text-xs text-muted-foreground mt-1">Min points to reach</p>
              </div>

              <div>
                <Label htmlFor="league_5_points">Diamond League</Label>
                <Input
                  id="league_5_points"
                  type="number"
                  min="0"
                  value={config.league_5_points}
                  onChange={(e) => setConfig({ ...config, league_5_points: parseInt(e.target.value) || 0 })}
                  className="mt-1"
                />
                <p className="text-xs text-muted-foreground mt-1">Min points to reach</p>
              </div>
            </div>
          </div>

          {/* League Discounts */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">League Discount Percentages</h3>
            <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
              <div>
                <Label htmlFor="league_1">Bronze League (Levels 1-{config.levels_per_league})</Label>
                <Input
                  id="league_1"
                  type="number"
                  min="0"
                  max="100"
                  step="0.1"
                  value={config.league_1_discount}
                  onChange={(e) => setConfig({ ...config, league_1_discount: parseFloat(e.target.value) || 0 })}
                  className="mt-1"
                />
                <p className="text-xs text-muted-foreground mt-1">Usually 0%</p>
              </div>
              
              <div>
                <Label htmlFor="league_2">Silver League (Levels {config.levels_per_league + 1}-{config.levels_per_league * 2})</Label>
                <Input
                  id="league_2"
                  type="number"
                  min="0"
                  max="100"
                  step="0.1"
                  value={config.league_2_discount}
                  onChange={(e) => setConfig({ ...config, league_2_discount: parseFloat(e.target.value) || 0 })}
                  className="mt-1"
                />
                <p className="text-xs text-muted-foreground mt-1">Recommended: 5%</p>
              </div>
              
              <div>
                <Label htmlFor="league_3">Gold League (Levels {config.levels_per_league * 2 + 1}-{config.levels_per_league * 3})</Label>
                <Input
                  id="league_3"
                  type="number"
                  min="0"
                  max="100"
                  step="0.1"
                  value={config.league_3_discount}
                  onChange={(e) => setConfig({ ...config, league_3_discount: parseFloat(e.target.value) || 0 })}
                  className="mt-1"
                />
                <p className="text-xs text-muted-foreground mt-1">Recommended: 10%</p>
              </div>
              
              <div>
                <Label htmlFor="league_4">Platinum League (Levels {config.levels_per_league * 3 + 1}-{config.levels_per_league * 4})</Label>
                <Input
                  id="league_4"
                  type="number"
                  min="0"
                  max="100"
                  step="0.1"
                  value={config.league_4_discount}
                  onChange={(e) => setConfig({ ...config, league_4_discount: parseFloat(e.target.value) || 0 })}
                  className="mt-1"
                />
                <p className="text-xs text-muted-foreground mt-1">Recommended: 15%</p>
              </div>
              
              <div>
                <Label htmlFor="league_5">Diamond League (Levels {config.levels_per_league * 4 + 1}-{config.levels_per_league * 5}+)</Label>
                <Input
                  id="league_5"
                  type="number"
                  min="0"
                  max="100"
                  step="0.1"
                  value={config.league_5_discount}
                  onChange={(e) => setConfig({ ...config, league_5_discount: parseFloat(e.target.value) || 0 })}
                  className="mt-1"
                />
                <p className="text-xs text-muted-foreground mt-1">Recommended: 20%</p>
              </div>
            </div>
          </div>

          {/* Global Multiplier */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <Zap className="h-5 w-5" />
              Global Points Multiplier
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="global_multiplier">Multiplier Value</Label>
                <Input
                  id="global_multiplier"
                  type="number"
                  min="0.1"
                  max="10"
                  step="0.1"
                  value={config.global_multiplier}
                  onChange={(e) => setConfig({ ...config, global_multiplier: parseFloat(e.target.value) || 1.0 })}
                  className="mt-1"
                />
                <p className="text-xs text-muted-foreground mt-1">
                  Applies to ALL users (e.g., 2.0 for Black Friday)
                </p>
              </div>
              
              <div>
                <Label htmlFor="multiplier_description">Description</Label>
                <Textarea
                  id="multiplier_description"
                  value={config.multiplier_description}
                  onChange={(e) => setConfig({ ...config, multiplier_description: e.target.value })}
                  className="mt-1"
                  rows={3}
                  placeholder="e.g., Black Friday 2x Points Event"
                />
                <p className="text-xs text-muted-foreground mt-1">
                  Internal description for this multiplier setting
                </p>
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="flex flex-wrap gap-4 pt-4 border-t">
            <Button onClick={saveConfig} disabled={loading || regenerating || updatingUsers} className="flex items-center gap-2">
              <Save className="h-4 w-4" />
              {loading ? 'Saving...' : 'Save Configuration'}
            </Button>

            <Button onClick={regenerateLevels} disabled={loading || regenerating || updatingUsers} variant="secondary" className="flex items-center gap-2">
              <RefreshCw className={`h-4 w-4 ${regenerating ? 'animate-spin' : ''}`} />
              {regenerating ? 'Regenerating...' : 'Regenerate Levels'}
            </Button>

            <Button onClick={updateUserLevels} disabled={loading || regenerating || updatingUsers} variant="secondary" className="flex items-center gap-2">
              <Users className={`h-4 w-4 ${updatingUsers ? 'animate-pulse' : ''}`} />
              {updatingUsers ? 'Updating Users...' : 'Update All Users'}
            </Button>

            <Button onClick={resetToDefaults} variant="outline" className="flex items-center gap-2">
              <RotateCcw className="h-4 w-4" />
              Reset to Defaults
            </Button>
          </div>

          <div className="text-sm text-muted-foreground mt-2 p-3 bg-blue-50 rounded-lg border border-blue-200">
            <p className="font-medium text-blue-800 mb-1">💡 Automatic Updates</p>
            <p className="text-blue-700">
              When you save the configuration, the system automatically regenerates all levels and updates all users to their correct levels based on their current points.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
