import { createClient } from '@/lib/supabase/server'
import { supabaseAdmin } from '@/lib/supabase/admin'
import { NextRequest, NextResponse } from 'next/server'
import { stripe } from '@/lib/stripe'
import { calculateVATByCategory } from '@/lib/utils'

// Function to handle guest user account (create or find existing)
// Currently unused but kept for potential future guest account functionality
// eslint-disable-next-line @typescript-eslint/no-unused-vars
async function handleGuestUser(email: string, firstName: string, lastName: string) {
  try {
    // First, check if user already exists in our users table
    const { data: existingUser, error: existingUserError } = await supabaseAdmin
      .from('users')
      .select('id, email, first_name, last_name')
      .eq('email', email)
      .single()

    if (existingUser && !existingUserError) {
      console.log('🛒 API: Found existing user for email:', email)
      return existingUser
    }

    // If user doesn't exist in our users table, try to create a new guest account
    console.log('🛒 API: Creating new guest account for email:', email)

    // Generate a temporary password
    const tempPassword = Math.random().toString(36).slice(-12) + 'A1!'

    // Create user with admin client
    const { data: authData, error: authError } = await supabaseAdmin.auth.admin.createUser({
      email,
      password: tempPassword,
      email_confirm: true, // Auto-confirm email
      user_metadata: {
        first_name: firstName,
        last_name: lastName,
        is_guest_account: true
      }
    })

    if (authError) {
      // If the error is that the email already exists in auth, try to get the user
      if (authError.code === 'email_exists') {
        console.log('🛒 API: Email exists in auth, trying to find user by email...')

        // Try to get the user by email from auth
        const { data: authUsers, error: listError } = await supabaseAdmin.auth.admin.listUsers()

        if (!listError && authUsers?.users) {
          const existingAuthUser = authUsers.users.find(u => u.email === email)

          if (existingAuthUser) {
            console.log('🛒 API: Found existing auth user, checking if user data exists...')

            // Check if user data exists in our users table
            const { data: userData, error: userDataError } = await supabaseAdmin
              .from('users')
              .select('id, email, first_name, last_name')
              .eq('id', existingAuthUser.id)
              .single()

            if (userData && !userDataError) {
              console.log('🛒 API: Found existing user data')
              return userData
            } else {
              // User exists in auth but not in our users table, create the user data
              console.log('🛒 API: Creating user data for existing auth user')
              const { data: newUserData, error: insertError } = await supabaseAdmin
                .from('users')
                .insert({
                  id: existingAuthUser.id,
                  email: existingAuthUser.email!,
                  first_name: firstName,
                  last_name: lastName,
                  is_admin: false,
                  total_points: 0,
                  lifetime_spend: 0,
                  current_level: 1
                })
                .select()
                .single()

              if (insertError) {
                console.error('Error inserting user data for existing auth user:', insertError)
                return null
              }

              return newUserData
            }
          }
        }
      }

      console.error('Error creating guest user:', authError)
      return null
    }

    // Insert user data into users table
    const { data: userData, error: userError } = await supabaseAdmin
      .from('users')
      .insert({
        id: authData.user.id,
        email: authData.user.email!,
        first_name: firstName,
        last_name: lastName,
        is_admin: false,
        total_points: 0,
        lifetime_spend: 0,
        current_level: 1
      })
      .select()
      .single()

    if (userError) {
      console.error('Error inserting user data:', userError)
      return null
    }

    // Send password reset email so user can set their own password
    await supabaseAdmin.auth.admin.generateLink({
      type: 'recovery',
      email: email,
      options: {
        redirectTo: `${process.env.NEXT_PUBLIC_APP_URL}/reset-password`
      }
    })

    return userData
  } catch (error) {
    console.error('Error in handleGuestUser:', error)
    return null
  }
}

export async function POST(request: NextRequest) {
  console.log('🛒 API: create-order called')

  try {
    // Check for required environment variables at runtime
    if (!process.env.STRIPE_SECRET_KEY) {
      console.error('🛒 API: STRIPE_SECRET_KEY is not set')
      return NextResponse.json(
        { error: 'Server configuration error' },
        { status: 500 }
      )
    }

    if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
      console.error('🛒 API: Supabase environment variables are not set')
      return NextResponse.json(
        { error: 'Server configuration error' },
        { status: 500 }
      )
    }

    if (!process.env.NEXT_PUBLIC_APP_URL) {
      console.error('🛒 API: NEXT_PUBLIC_APP_URL is not set')
      return NextResponse.json(
        { error: 'Server configuration error' },
        { status: 500 }
      )
    }

    console.log('🛒 API: Parsing request body')
    const requestBody = await request.json()
    console.log('🛒 API: Request body:', {
      hasCartId: !!requestBody.cartId,
      hasCustomerInfo: !!requestBody.customerInfo,
      hasShippingAddress: !!requestBody.shippingAddress,
      hasBillingAddress: !!requestBody.billingAddress,
      hasPaymentMethodId: !!requestBody.paymentMethodId,
      hasTotal: !!requestBody.total,
      hasCoupon: !!requestBody.coupon,
      cartId: requestBody.cartId,
      total: requestBody.total,
      coupon: requestBody.coupon
    })

    const {
      cartId,
      customerInfo,
      shippingAddress,
      billingAddress,
      total,
      coupon
    } = requestBody

    if (!cartId || !customerInfo || !shippingAddress || !billingAddress || !total) {
      console.error('🛒 API: Missing required fields:', {
        cartId: !!cartId,
        customerInfo: !!customerInfo,
        shippingAddress: !!shippingAddress,
        billingAddress: !!billingAddress,
        total: !!total
      })
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    console.log('🛒 API: Creating Supabase client')
    const supabase = await createClient()

    // Get current user - handle AuthSessionMissingError gracefully for guest checkout
    console.log('🛒 API: Getting current user')
    let user = null
    let userError = null

    try {
      const { data: userData, error: authError } = await supabase.auth.getUser()
      user = userData.user
      userError = authError
    } catch (error) {
      // Handle AuthSessionMissingError and other auth errors gracefully
      if (error instanceof Error && error.message.includes('Auth session missing')) {
        console.log('🛒 API: No auth session found - processing as guest checkout')
        user = null
        userError = null
      } else {
        console.error('🛒 API: Unexpected error getting user:', error)
        userError = error
      }
    }

    if (userError && !(userError instanceof Error && userError.message?.includes('Auth session missing'))) {
      console.error('🛒 API: Error getting user:', userError)
      // Don't fail the request for auth errors - allow guest checkout
    }

    console.log('🛒 API: User data:', { userId: user?.id, userEmail: user?.email })

    // Handle guest users - for true guest checkout, don't create account
    let finalUser: { id: string; email: string } | null = user ? { id: user.id, email: user.email || '' } : null
    if (!user) {
      console.log('🛒 API: No authenticated user, processing as guest order (no account creation)')
      // For guest orders, finalUser remains null so user_id will be NULL in the database
      finalUser = null
    } else {
      console.log('🛒 API: User is authenticated, using existing user:', { userId: user.id, email: user.email })
    }

    // Get cart with items
    console.log('🛒 API: Fetching cart with ID:', cartId)
    console.log('🛒 API: Request data received:', {
      cartId,
      customerInfo: {
        firstName: customerInfo?.firstName,
        lastName: customerInfo?.lastName,
        email: customerInfo?.email,
        phone: customerInfo?.phone
      },
      shippingAddress: shippingAddress ? {
        street: shippingAddress.street,
        city: shippingAddress.city,
        postalCode: shippingAddress.postalCode,
        country: shippingAddress.country
      } : null,
      total
    })

    let cart
    try {
      const { data: cartData, error: cartError } = await supabase
        .from('carts')
        .select(`
          *,
          cart_items (
            *,
            products (
              id,
              title,
              price,
              discount_price
            )
          )
        `)
        .eq('id', cartId)
        .single()

      if (cartError) {
        console.error('🛒 API: Error fetching cart:', cartError)
        console.error('🛒 API: Cart error details:', {
          message: cartError.message,
          details: cartError.details,
          hint: cartError.hint,
          code: cartError.code
        })
        return NextResponse.json(
          { error: 'Cart not found', details: cartError.message },
          { status: 404 }
        )
      }

      cart = cartData
    } catch (error) {
      console.error('🛒 API: Exception while fetching cart:', error)
      return NextResponse.json(
        { error: 'Failed to fetch cart data' },
        { status: 500 }
      )
    }

    if (!cart) {
      console.error('🛒 API: Cart not found for ID:', cartId)
      return NextResponse.json(
        { error: 'Cart not found' },
        { status: 404 }
      )
    }

    console.log('🛒 API: Cart retrieved:', {
      cartId: cart.id,
      itemsCount: cart.cart_items?.length || 0,
      status: cart.status,
      totalAmount: cart.total_amount
    })

    // Get site settings for VAT calculation
    const { data: siteSettings } = await supabaseAdmin
      .from('site_settings')
      .select('use_category_vat, vat_rate, vat_rate_coffee, vat_rate_accessories')
      .maybeSingle()

    const vatSettings = siteSettings || {
      use_category_vat: false,
      vat_rate: 0.077,
      vat_rate_coffee: 0.077,
      vat_rate_accessories: 0.077
    }

    // Calculate totals - prices are VAT-inclusive
    const subtotal = cart.cart_items.reduce((sum: number, item: {
      quantity: number
      products: {
        price: number
        discount_price?: number
      }
    }) => {
      const price = item.products.discount_price || item.products.price
      return sum + (price * item.quantity)
    }, 0)

    const shippingCost = subtotal >= 90 ? 0 : 8.90
    const couponDiscountAmount = coupon?.discountAmount || 0

    // Calculate league discount for logged-in users
    let leagueDiscountAmount = 0
    if (user) {
      const { data: userProfile } = await supabaseAdmin
        .from('users')
        .select('current_level')
        .eq('id', user.id)
        .single()

      if (userProfile?.current_level) {
        const { calculateLeague, getLeagueDiscount } = await import('@/lib/gamification')
        const league = calculateLeague(userProfile.current_level)
        const discountPercentage = await getLeagueDiscount(league)
        leagueDiscountAmount = subtotal * (discountPercentage / 100)
      }
    }

    const totalDiscountAmount = couponDiscountAmount + leagueDiscountAmount
    const totalAmount = subtotal + shippingCost - totalDiscountAmount // Total is VAT-inclusive

    // Calculate VAT breakdown by category (VAT is included in prices)
    const vatBreakdown = calculateVATByCategory(
      cart.cart_items.map((item: { quantity: number; products: { price: number; discount_price?: number; category: string } }) => ({
        quantity: item.quantity,
        products: {
          price: item.products.price,
          discount_price: item.products.discount_price,
          category: item.products.category as 'coffee' | 'accessories'
        }
      })),
      vatSettings
    )

    // Calculate VAT on shipping and discount (using general VAT rate)
    const shippingVAT = shippingCost > 0 ? shippingCost - (shippingCost / (1 + (vatSettings.vat_rate || 0.077))) : 0
    const discountVAT = totalDiscountAmount > 0 ? totalDiscountAmount - (totalDiscountAmount / (1 + (vatSettings.vat_rate || 0.077))) : 0

    const taxAmount = vatBreakdown.totalVAT + shippingVAT - discountVAT

    console.log('🛒 API: Calculated totals:', {
      subtotal,
      shippingCost,
      couponDiscountAmount,
      leagueDiscountAmount,
      totalDiscountAmount,
      taxAmount,
      vatBreakdown: {
        coffeeVAT: vatBreakdown.coffeeVAT,
        accessoriesVAT: vatBreakdown.accessoriesVAT,
        shippingVAT,
        discountVAT,
        totalVAT: taxAmount
      },
      totalAmount,
      amountInCents: Math.round(totalAmount * 100)
    })

    // Create Stripe Payment Intent (without immediate confirmation)
    console.log('🛒 API: Creating Stripe Payment Intent...')
    let paymentIntent
    try {
      paymentIntent = await stripe.paymentIntents.create({
        amount: Math.round(totalAmount * 100), // Convert to cents
        currency: 'chf',
        automatic_payment_methods: {
          enabled: true,
          allow_redirects: 'always'
        },
        payment_method_types: ['card', 'twint', 'apple_pay', 'google_pay', 'link'],
        metadata: {
          cartId: cartId,
          customerEmail: customerInfo.email,
          userId: finalUser?.id || 'guest'
        },
        shipping: {
          name: `${customerInfo.firstName} ${customerInfo.lastName}`,
          phone: customerInfo.phone,
          address: {
            line1: shippingAddress.street,
            city: shippingAddress.city,
            postal_code: shippingAddress.postalCode,
            country: shippingAddress.country,
          }
        },
        receipt_email: customerInfo.email
      })

      console.log('🛒 API: Stripe Payment Intent created successfully:', {
        id: paymentIntent.id,
        status: paymentIntent.status,
        amount: paymentIntent.amount,
        currency: paymentIntent.currency
      })
    } catch (stripeError) {
      console.error('🛒 API: Error creating Stripe payment intent:', stripeError)
      return NextResponse.json(
        { error: 'Failed to create payment intent', details: stripeError instanceof Error ? stripeError.message : String(stripeError) },
        { status: 500 }
      )
    }

    // Create order
    console.log('🛒 API: Creating order in database...')
    let order
    try {
      // Use admin client to bypass RLS for guest orders
      const { data: orderData, error: orderError } = await supabaseAdmin
        .from('orders')
        .insert({
          user_id: finalUser?.id || null,
          email: customerInfo.email,
          status: 'pending',
          subtotal: subtotal,
          shipping_cost: shippingCost,
          tax_amount: taxAmount,
          discount_amount: totalDiscountAmount,
          total_amount: totalAmount,
          currency: 'CHF',
          payment_status: 'pending',
          payment_intent_id: paymentIntent.id,
          shipping_address: {
            firstName: shippingAddress.firstName || customerInfo.firstName,
            lastName: shippingAddress.lastName || customerInfo.lastName,
            company: shippingAddress.company || '',
            phone: customerInfo.phone,
            street: shippingAddress.street,
            city: shippingAddress.city,
            postalCode: shippingAddress.postalCode,
            country: shippingAddress.country
          },
          billing_address: {
            firstName: billingAddress.firstName || customerInfo.firstName,
            lastName: billingAddress.lastName || customerInfo.lastName,
            company: billingAddress.company || '',
            phone: customerInfo.phone,
            street: billingAddress.street,
            city: billingAddress.city,
            postalCode: billingAddress.postalCode,
            country: billingAddress.country
          }
        })
        .select()
        .single()

      if (orderError) {
        console.error('🛒 API: Error creating order:', orderError)
        console.error('🛒 API: Order error details:', {
          message: orderError.message,
          details: orderError.details,
          hint: orderError.hint,
          code: orderError.code
        })
        return NextResponse.json(
          { error: 'Failed to create order', details: orderError.message },
          { status: 500 }
        )
      }

      order = orderData
    } catch (error) {
      console.error('🛒 API: Exception while creating order:', error)
      return NextResponse.json(
        { error: 'Failed to create order in database' },
        { status: 500 }
      )
    }

    // Create order items
    const orderItems = cart.cart_items.map((item: {
      product_id: string
      quantity: number
      products: {
        price: number
        discount_price?: number
      }
    }) => ({
      order_id: order.id,
      product_id: item.product_id,
      quantity: item.quantity,
      unit_price: item.products.discount_price || item.products.price,
      total_price: (item.products.discount_price || item.products.price) * item.quantity
    }))

    const { error: itemsError } = await supabaseAdmin
      .from('order_items')
      .insert(orderItems)

    if (itemsError) {
      console.error('Error creating order items:', itemsError)
      // Rollback order creation
      await supabaseAdmin.from('orders').delete().eq('id', order.id)
      return NextResponse.json(
        { error: 'Failed to create order items' },
        { status: 500 }
      )
    }

    // Update coupon usage count if coupon was applied
    if (coupon?.id) {
      console.log('🛒 API: Updating coupon usage count for coupon:', coupon.id)
      // First get current used_count
      const { data: currentCoupon } = await supabaseAdmin
        .from('coupons')
        .select('used_count')
        .eq('id', coupon.id)
        .single()

      const { error: couponError } = await supabaseAdmin
        .from('coupons')
        .update({
          used_count: (currentCoupon?.used_count || 0) + 1
        })
        .eq('id', coupon.id)

      if (couponError) {
        console.error('Error updating coupon usage:', couponError)
        // Don't fail the order creation for coupon update errors
      }
    }

    // Get user's gifts ready for order and add them to the order
    if (user) {
      const { getUserGiftsReadyForOrder, markGiftsAsAddedToOrder } = await import('@/lib/gamification')
      const giftsReadyForOrder = await getUserGiftsReadyForOrder(user.id)

      if (giftsReadyForOrder.length > 0) {
        console.log(`🎁 Order: Adding ${giftsReadyForOrder.length} gifts to order ${order.id}`)

        // Create order items for gifts
        const giftOrderItems = giftsReadyForOrder
          .filter(gift => gift.selected_product_id) // Only include gifts with selected products
          .map((gift) => ({
            order_id: order.id,
            product_id: gift.selected_product_id!,
            quantity: 1,
            unit_price: 0, // Gifts are free
            total_price: 0,
            is_gift: true,
            gift_claim_id: gift.id
          }))

        const { error: giftItemsError } = await supabaseAdmin
          .from('order_items')
          .insert(giftOrderItems)

        if (giftItemsError) {
          console.error('Error creating gift order items:', giftItemsError)
        } else {
          // Mark gifts as added to order
          const claimIds = giftsReadyForOrder.map((gift) => gift.id)
          await markGiftsAsAddedToOrder(user.id, claimIds, order.id)
          console.log(`🎁 Order: Successfully added ${giftsReadyForOrder.length} gifts to order`)
        }
      }

      // Update user gamification after successful order
      const { updateUserGamification } = await import('@/lib/gamification')
      await updateUserGamification(user.id, totalAmount, order.id)
    }

    // Return payment intent for client-side confirmation
    return NextResponse.json({
      success: true,
      orderId: order.id,
      paymentIntent: {
        id: paymentIntent.id,
        client_secret: paymentIntent.client_secret,
        status: paymentIntent.status
      }
    })

  } catch (error) {
    console.error('🛒 API: Exception in create-order:', error)
    console.error('🛒 API: Exception details:', {
      name: error instanceof Error ? error.name : 'Unknown',
      message: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined
    })
    return NextResponse.json(
      { error: 'Internal server error', details: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    )
  }
}
