# Sistema di Aggiornamento Automatico della Gamification

## Panoramica

Il sistema di gamification di PrimeCaffe è stato migliorato per garantire che quando l'admin modifica i punti necessari per raggiungere le leghe, tutti i calcoli dei livelli si adattino automaticamente e tutti gli utenti vengano aggiornati al livello corretto.

## Come Funziona

### 1. Configurazione delle Leghe

L'admin può configurare:
- **Livelli per lega**: Numero di livelli in ogni lega (default: 10)
- **Punti per lega**: Punti necessari per raggiungere ogni lega
  - Lega Bronze: 0 punti (default)
  - Lega Silver: 1000 punti (default)
  - Lega Gold: 3000 punti (default)
  - Lega Platinum: 6000 punti (default)
  - Lega Diamond: 10000 punti (default)
- **Sconti per lega**: Percentuale di sconto per ogni lega
- **Moltiplicatore globale**: Moltiplicatore per i punti guadagnati

### 2. Generazione Automatica dei Livelli

Quando l'admin salva la configurazione, il sistema:

1. **Cancella tutti i livelli esistenti** dalla tabella `user_levels`
2. **Calcola automaticamente i punti per ogni livello** usando la formula:
   ```
   punti_per_livello = (punti_fine_lega - punti_inizio_lega) / livelli_per_lega
   ```
3. **Genera tutti i livelli** per tutte le leghe con distribuzione uniforme
4. **Aggiorna automaticamente tutti gli utenti** al livello corretto basato sui loro punti attuali

### 3. Esempio di Calcolo

Se l'admin imposta:
- Livelli per lega: 10
- Lega Silver: 2000 punti (invece di 1000)
- Lega Gold: 5000 punti (invece di 3000)

Il sistema calcolerà automaticamente:
- **Lega Bronze (livelli 1-10)**: 0-2000 punti
  - Livello 1: 0 punti
  - Livello 2: 200 punti
  - Livello 3: 400 punti
  - ...
  - Livello 10: 1800 punti

- **Lega Silver (livelli 11-20)**: 2000-5000 punti
  - Livello 11: 2000 punti
  - Livello 12: 2300 punti
  - Livello 13: 2600 punti
  - ...
  - Livello 20: 4700 punti

### 4. Aggiornamento Automatico degli Utenti

Dopo la rigenerazione dei livelli, il sistema:
1. **Controlla tutti gli utenti** con punti assegnati
2. **Calcola il livello corretto** per ogni utente basato sui nuovi requisiti
3. **Aggiorna automaticamente** il campo `current_level` per ogni utente
4. **Fornisce un report** del numero di utenti aggiornati

## Funzioni SQL

### `generate_levels_from_config()`
- Legge la configurazione attiva dalla tabella `league_config`
- Cancella tutti i livelli esistenti
- Genera nuovi livelli con distribuzione automatica dei punti
- Crea nomi dei livelli (Bronze I, Bronze II, Silver I, etc.)

### `update_all_user_levels()`
- Trova il livello corretto per ogni utente basato sui punti totali
- Aggiorna solo gli utenti che hanno un livello scorretto
- Restituisce il numero di utenti aggiornati e il totale processato

## API Endpoints

### `POST /api/admin/gamification/league-config`
- Salva la configurazione delle leghe
- Rigenera automaticamente tutti i livelli
- Aggiorna automaticamente tutti gli utenti
- Restituisce conferma del successo

### `POST /api/admin/gamification/regenerate-levels`
- Rigenera manualmente i livelli dalla configurazione corrente
- Utile per debug o correzioni manuali

### `POST /api/admin/gamification/update-user-levels`
- Aggiorna manualmente tutti gli utenti al livello corretto
- Utile per correzioni o verifiche

## Interfaccia Admin

### Pagina Gamification (`/admin/gamification`)
- **Configurazione Punti per CHF**: Imposta quanti punti per ogni CHF speso
- **Gestione Leghe**: Configura punti e sconti per ogni lega
- **Pulsanti di Azione**:
  - **Salva Configurazione**: Salva e applica automaticamente tutti gli aggiornamenti
  - **Rigenera Livelli**: Rigenera manualmente i livelli
  - **Aggiorna Tutti gli Utenti**: Aggiorna manualmente tutti gli utenti
  - **Reset ai Default**: Ripristina la configurazione predefinita

### Feedback Visivo
- Messaggi di conferma per ogni operazione
- Indicatori di caricamento durante le operazioni
- Contatori degli utenti aggiornati
- Avvisi informativi sul funzionamento automatico

## Pagina Account Utente

### Visualizzazione Dinamica
La pagina account dell'utente (`/account`) mostra sempre i dati aggiornati:
- **Livello corrente** basato sui punti attuali
- **Progresso verso il prossimo livello** con barra di avanzamento
- **Lega attuale** con icone e colori appropriati
- **Sconto attuale** basato sulla lega
- **Punti mancanti** per il prossimo livello o lega

### Aggiornamento in Tempo Reale
Quando l'admin modifica la configurazione:
1. I livelli vengono rigenerati automaticamente
2. Gli utenti vengono aggiornati al livello corretto
3. La prossima volta che l'utente visita la pagina account, vedrà i dati aggiornati
4. Non è necessario alcun intervento manuale

## Vantaggi del Sistema

### 1. **Completamente Automatico**
- Nessun intervento manuale richiesto
- Aggiornamenti istantanei quando l'admin salva la configurazione
- Consistenza garantita tra configurazione e dati utente

### 2. **Scalabile**
- Funziona con qualsiasi numero di utenti
- Calcoli efficienti tramite funzioni SQL
- Aggiornamenti batch per performance ottimali

### 3. **Flessibile**
- L'admin può modificare qualsiasi aspetto della configurazione
- Il sistema si adatta automaticamente a qualsiasi cambiamento
- Supporta configurazioni personalizzate

### 4. **Sicuro**
- Tutte le operazioni sono transazionali
- Rollback automatico in caso di errori
- Validazione dei dati di input

### 5. **Trasparente**
- Feedback chiaro all'admin su cosa è stato fatto
- Log dettagliati per debugging
- Test automatizzati per garantire il funzionamento

## Test

Il sistema include test completi che verificano:
- Generazione corretta dei livelli
- Aggiornamento corretto degli utenti
- Gestione degli errori
- Calcoli matematici dei punti per livello
- Workflow completo di aggiornamento della configurazione

## Conclusione

Questo sistema garantisce che il sistema di gamification di PrimeCaffe sia sempre coerente e aggiornato, indipendentemente dalle modifiche apportate dall'admin alla configurazione delle leghe. Gli utenti vedranno sempre i dati corretti e l'admin può modificare la configurazione con la certezza che tutto si aggiornerà automaticamente.
