'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useTranslations, useLocale } from 'next-intl'
import { createClient } from '@/lib/supabase/client'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { ArrowLeft } from 'lucide-react'
import Link from 'next/link'
import ProductForm from '@/components/admin/product-form'

interface Product {
  id: string;
  title: string;
  description: string;
  category: string;
  coffee_type?: string;
  brand?: string;
  blend?: string;
  machine_compatibility?: string[];
  pack_quantity?: number;
  pack_weight_grams?: number;
  price: number;
  discount_price?: number;
  cost_per_espresso?: number;
  inventory_count: number;
  purchase_cost?: number;
  is_available: boolean;
  created_at: string;
  images: string[];
  title_template?: string;
  template_id?: string;
  translations?: { [key: string]: { title: string; description: string } } | null;
}

interface EditProductPageProps {
  params: Promise<{ locale: string; id: string }>;
}

export default function EditProductPage({ params }: EditProductPageProps) {
  const locale = useLocale()
  const router = useRouter()
  const t = useTranslations('admin')
  const supabase = createClient()

  const [loading, setLoading] = useState(true)
  const [authChecked, setAuthChecked] = useState(false)
  const [product, setProduct] = useState<Product | null>(null)
  const [productId, setProductId] = useState<string>('')

  useEffect(() => {
    const getParams = async () => {
      const resolvedParams = await params
      setProductId(resolvedParams.id)
    }
    getParams()
  }, [params])

  useEffect(() => {
    const checkAuthAndLoadData = async () => {
      if (!productId) return

      try {
        console.log('Checking auth for edit product page')
        
        const { data: { user } } = await supabase.auth.getUser()
        
        if (!user) {
          console.log('No user found, redirecting to login')
          router.push(`/${locale}/login`)
          return
        }

        console.log('User found, checking admin status')

        const { data: profile, error: profileError } = await supabase
          .from('users')
          .select('is_admin')
          .eq('id', user.id)
          .single()

        if (profileError) {
          console.error('Profile error:', profileError)
          setAuthChecked(true)
          setLoading(false)
          return
        }

        console.log('Profile loaded:', profile)

        if (!profile?.is_admin) {
          console.log('User is not admin, redirecting')
          setAuthChecked(true)
          router.push(`/${locale}`)
          return
        }

        console.log('User is admin, loading product')
        setAuthChecked(true)
        
        // Load product with translations using API
        const response = await fetch(`/api/admin/products/${productId}`)
        if (!response.ok) {
          console.error('Error fetching product from API')
          router.push(`/${locale}/admin/products`)
          return
        }

        const { product: productData } = await response.json()
        if (!productData) {
          console.log('Product not found')
          router.push(`/${locale}/admin/products`)
          return
        }

        setProduct(productData)
        setLoading(false)
      } catch (error) {
        console.error('Error in checkAuthAndLoadData:', error)
        setAuthChecked(true)
        setLoading(false)
      }
    }

    if (!authChecked && productId) {
      checkAuthAndLoadData()
    }
  }, [authChecked, locale, router, supabase, productId])

  if (loading) {
    return (
      <div className="container mx-auto py-8">
        <div className="flex justify-center items-center min-h-[400px]">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </div>
    )
  }

  if (!product) {
    return (
      <div className="container mx-auto py-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold">{t('products.edit.notFound')}</h1>
          <Button asChild className="mt-4">
            <Link href={`/${locale}/admin/products`}>
              {t('common.back')}
            </Link>
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto py-8">
      <div className="flex items-center gap-4 mb-8">
        <Button variant="outline" size="sm" asChild>
          <Link href={`/${locale}/admin/products`}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            {t('common.back')}
          </Link>
        </Button>
        <div>
          <h1 className="text-3xl font-bold">{t('products.edit.title')}</h1>
          <p className="text-muted-foreground">
            {t('products.edit.description')}
          </p>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>{t('products.edit.formTitle')}</CardTitle>
        </CardHeader>
        <CardContent>
          <ProductForm locale={locale} product={product} />
        </CardContent>
      </Card>
    </div>
  )
}
