import { useState, useEffect } from 'react'
import { createClient } from '@/lib/supabase/client'

interface ShippingCountry {
  code: string
  name: string
  cost: number
  free_shipping_threshold: number
  estimated_days: string
}

interface UseShippingCountriesReturn {
  countries: ShippingCountry[]
  loading: boolean
  error: string | null
  getShippingCost: (countryCode: string, orderTotal: number) => number
  getCountryName: (countryCode: string) => string
}

// Country code to name mapping
const COUNTRY_NAMES: Record<string, string> = {
  'CH': 'Svizzera',
  'IT': 'Italia', 
  'DE': 'Germania',
  'FR': 'Francia',
  'AT': 'Austria',
  'ES': 'Spagna',
  'NL': 'Paesi <PERSON>i',
  'BE': 'Belgio',
  'LU': 'Lussemburgo',
  'LI': 'Liechtenstein'
}

export function useShippingCountries(): UseShippingCountriesReturn {
  const [countries, setCountries] = useState<ShippingCountry[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const supabase = createClient()

  useEffect(() => {
    const loadShippingCountries = async () => {
      try {
        setLoading(true)
        setError(null)

        const { data, error: fetchError } = await supabase
          .from('shipping_rates')
          .select('country, cost, free_shipping_threshold, estimated_days')
          .eq('is_active', true)
          .order('country')

        if (fetchError) {
          console.error('Error loading shipping countries:', fetchError)
          setError('Failed to load shipping countries')
          return
        }

        const shippingCountries: ShippingCountry[] = (data || []).map(rate => ({
          code: rate.country,
          name: COUNTRY_NAMES[rate.country] || rate.country,
          cost: rate.cost,
          free_shipping_threshold: rate.free_shipping_threshold || 0,
          estimated_days: rate.estimated_days
        }))

        setCountries(shippingCountries)
      } catch (err) {
        console.error('Error loading shipping countries:', err)
        setError(err instanceof Error ? err.message : 'Unknown error')
      } finally {
        setLoading(false)
      }
    }

    loadShippingCountries()
  }, [supabase])

  const getShippingCost = (countryCode: string, orderTotal: number): number => {
    const country = countries.find(c => c.code === countryCode)
    if (!country) return 0

    if (country.free_shipping_threshold && orderTotal >= country.free_shipping_threshold) {
      return 0
    }

    return country.cost
  }

  const getCountryName = (countryCode: string): string => {
    const country = countries.find(c => c.code === countryCode)
    return country?.name || COUNTRY_NAMES[countryCode] || countryCode
  }

  return {
    countries,
    loading,
    error,
    getShippingCost,
    getCountryName
  }
}
