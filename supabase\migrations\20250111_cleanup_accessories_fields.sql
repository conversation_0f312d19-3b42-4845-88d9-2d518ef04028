-- Migration to clean up coffee-specific fields for accessories products
-- This ensures data consistency by setting coffee-specific fields to null for accessories

-- Clean up coffee-specific fields for accessories products
UPDATE products 
SET 
    coffee_type = NULL,
    blend = NULL,
    pack_quantity = NULL,
    pack_weight_grams = NULL,
    cost_per_espresso = NULL,
    machine_compatibility = NULL
WHERE category = 'accessories';

-- Add a comment to document the change
COMMENT ON TABLE products IS 'Products table - coffee-specific fields (coffee_type, blend, pack_quantity, pack_weight_grams, cost_per_espresso, machine_compatibility) should be NULL for accessories category';
