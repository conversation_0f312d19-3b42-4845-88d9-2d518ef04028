-- Migration: Add function to update all user levels after configuration changes
-- Date: 2025-07-14

-- <PERSON><PERSON> function to update all user levels after configuration changes
CREATE OR REPLACE FUNCTION update_all_user_levels()
RETURNS TABLE(users_updated INTEGER, total_users INTEGER)
LANGUAGE plpgsql
AS $$
DECLARE
    user_record RECORD;
    correct_level_record user_levels%ROWTYPE;
    users_updated_count INTEGER := 0;
    total_users_count INTEGER := 0;
BEGIN
    -- Count total users
    SELECT COUNT(*) INTO total_users_count FROM users WHERE total_points IS NOT NULL;
    
    -- Update each user's level based on their current points
    FOR user_record IN 
        SELECT id, total_points, current_level 
        FROM users 
        WHERE total_points IS NOT NULL
    LOOP
        total_users_count := total_users_count + 1;
        
        -- Find the correct level for this user's points
        SELECT * INTO correct_level_record
        FROM user_levels
        WHERE minimum_points <= COALESCE(user_record.total_points, 0)
        ORDER BY level DESC
        LIMIT 1;
        
        -- Update user if their current level is incorrect
        IF correct_level_record.level IS NOT NULL AND 
           (user_record.current_level IS NULL OR user_record.current_level != correct_level_record.level) THEN
            
            UPDATE users 
            SET current_level = correct_level_record.level
            WHERE id = user_record.id;
            
            users_updated_count := users_updated_count + 1;
            
            RAISE NOTICE 'Updated user % from level % to level %', 
                user_record.id, 
                COALESCE(user_record.current_level, 0), 
                correct_level_record.level;
        END IF;
    END LOOP;
    
    RETURN QUERY SELECT users_updated_count, total_users_count;
END;
$$;

-- Add comment
COMMENT ON FUNCTION update_all_user_levels() IS 'Updates all users to their correct level based on current points and level configuration. Returns count of updated users and total users processed.';
