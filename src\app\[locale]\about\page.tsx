"use client";

import { useTranslations } from 'next-intl';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Coffee, Heart, Award, Users, Leaf, Clock } from 'lucide-react';
import Image from 'next/image';

export default function AboutPage() {
  const t = useTranslations('about');

  return (
    <div className="container mx-auto px-4 py-12">
      {/* Hero Section */}
      <div className="text-center mb-16">
        <h1 className="text-4xl lg:text-5xl font-bold mb-6">
          {t('title')} <span className="text-primary">PrimeCaffe</span>
        </h1>
        <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
          {t('subtitle')}
        </p>
      </div>

      {/* Story Section */}
      <div className="grid lg:grid-cols-2 gap-12 mb-16">
        <div>
          <h2 className="text-3xl font-bold mb-6">{t('story.title')}</h2>
          <div className="space-y-4 text-muted-foreground">
            <p>{t('story.content')}</p>
            <p>{t('story.paragraphs.0')}</p>
            <p>{t('story.paragraphs.1')}</p>
          </div>
        </div>
        <div className="relative rounded-2xl overflow-hidden
          shadow-[0_20px_50px_rgba(0,0,0,0.15),0_10px_25px_rgba(0,0,0,0.1),0_4px_10px_rgba(0,0,0,0.08)]
          hover:shadow-[0_25px_60px_rgba(0,0,0,0.2),0_15px_35px_rgba(0,0,0,0.15),0_8px_15px_rgba(0,0,0,0.1)]
          transition-all duration-500 ease-out hover:scale-[1.02] border border-muted/20">
          <Image
            src="/images/chi-siamo.webp"
            alt="Chi Siamo - Il Team PrimeCaffe"
            width={500}
            height={400}
            className="w-full h-auto object-cover"
            sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 500px"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent"></div>
          {/* Premium glow effect */}
          <div className="absolute -inset-1 bg-gradient-to-r from-primary/20 via-primary/10 to-primary/20 rounded-2xl blur-sm opacity-0 hover:opacity-100 transition-opacity duration-500 -z-10"></div>
        </div>
      </div>

      {/* Mission Section */}
      <div className="mb-16 text-center">
        <h2 className="text-3xl font-bold mb-6">{t('mission.title')}</h2>
        <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
          {t('mission.content')}
        </p>
      </div>

      {/* Values Section */}
      <div className="mb-16">
        <h2 className="text-3xl font-bold text-center mb-12">{t('values.title')}</h2>
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          <Card>
            <CardHeader className="text-center">
              <div className="mx-auto w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4">
                <Award className="h-6 w-6 text-primary" />
              </div>
              <CardTitle>{t('values.quality.title')}</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-center text-muted-foreground">
                {t('values.quality.description')}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="text-center">
              <div className="mx-auto w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4">
                <Heart className="h-6 w-6 text-primary" />
              </div>
              <CardTitle>{t('values.passion.title')}</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-center text-muted-foreground">
                {t('values.passion.description')}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="text-center">
              <div className="mx-auto w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4">
                <Leaf className="h-6 w-6 text-primary" />
              </div>
              <CardTitle>{t('values.sustainability.title')}</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-center text-muted-foreground">
                {t('values.sustainability.description')}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="text-center">
              <div className="mx-auto w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4">
                <Users className="h-6 w-6 text-primary" />
              </div>
              <CardTitle>{t('values.service.title')}</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-center text-muted-foreground">
                {t('values.service.description')}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="text-center">
              <div className="mx-auto w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4">
                <Clock className="h-6 w-6 text-primary" />
              </div>
              <CardTitle>{t('values.freshness.title')}</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-center text-muted-foreground">
                {t('values.freshness.description')}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="text-center">
              <div className="mx-auto w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4">
                <Coffee className="h-6 w-6 text-primary" />
              </div>
              <CardTitle>{t('values.innovation.title')}</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-center text-muted-foreground">
                {t('values.innovation.description')}
              </p>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Contact CTA */}
      <div className="text-center bg-muted/50 rounded-lg p-12">
        <h2 className="text-2xl font-bold mb-4">{t('contactCta.title')}</h2>
        <p className="text-muted-foreground mb-6">{t('contactCta.description')}</p>
        <div className="space-y-2">
          <p className="font-semibold"><EMAIL></p>
          <p className="text-muted-foreground">+41 79 342 65 74</p>
        </div>
      </div>
    </div>
  );
}
