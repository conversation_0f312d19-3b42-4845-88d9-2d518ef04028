'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { locales, localeNames, localeFlags, type Locale } from '@/lib/i18n/config';
import { useTranslations } from 'next-intl';

interface LanguageTabsProps {
  translations: {
    [key in Locale]?: {
      title: string;
      description: string;
    };
  };
  onTranslationChange: (locale: Locale, field: 'title' | 'description', value: string) => void;
  defaultLocale?: Locale;
  className?: string;
}

export function LanguageTabs({
  translations,
  onTranslationChange,
  defaultLocale = 'de',
  className = ''
}: LanguageTabsProps) {
  const [activeTab, setActiveTab] = useState<Locale>(defaultLocale);
  const t = useTranslations('admin.products.productForm');

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <span>{t('translations')}</span>
          <span className="text-sm text-muted-foreground">
            ({t('translationsHelp')})
          </span>
        </CardTitle>
        
        {/* Language Tabs */}
        <div className="flex gap-1 p-1 bg-muted rounded-lg">
          {locales.map((locale) => (
            <Button
              key={locale}
              type="button"
              variant={activeTab === locale ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setActiveTab(locale)}
              className="flex items-center gap-2 flex-1"
            >
              <span>{localeFlags[locale]}</span>
              <span className="hidden sm:inline">{localeNames[locale]}</span>
              <span className="sm:hidden">{locale.toUpperCase()}</span>
              {translations[locale]?.title && (
                <div className="w-2 h-2 bg-green-500 rounded-full ml-1" />
              )}
            </Button>
          ))}
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Active Language Form */}
        <div className="space-y-4">
          <div className="flex items-center gap-2 mb-4">
            <span className="text-lg">{localeFlags[activeTab]}</span>
            <h3 className="text-lg font-semibold">
              {localeNames[activeTab]}
            </h3>
            {activeTab === defaultLocale && (
              <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                {t('defaultLanguage')}
              </span>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor={`title-${activeTab}`}>
              {t('title')} ({localeNames[activeTab]})
            </Label>
            <Input
              id={`title-${activeTab}`}
              type="text"
              value={translations[activeTab]?.title || ''}
              onChange={(e) => onTranslationChange(activeTab, 'title', e.target.value)}
              placeholder={t('titlePlaceholder')}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor={`description-${activeTab}`}>
              {t('description')} ({localeNames[activeTab]})
            </Label>
            <Textarea
              id={`description-${activeTab}`}
              value={translations[activeTab]?.description || ''}
              onChange={(e) => onTranslationChange(activeTab, 'description', e.target.value)}
              placeholder={t('descriptionPlaceholder')}
              rows={4}
            />
          </div>
        </div>

        {/* Translation Status */}
        <div className="mt-6 p-4 bg-muted/50 rounded-lg">
          <h4 className="text-sm font-medium mb-2">{t('translationStatus')}</h4>
          <div className="grid grid-cols-3 gap-2">
            {locales.map((locale) => {
              const hasTranslation = translations[locale]?.title && translations[locale]?.description;
              return (
                <div
                  key={locale}
                  className={`flex items-center gap-2 p-2 rounded text-sm ${
                    hasTranslation
                      ? 'bg-green-100 text-green-800'
                      : 'bg-gray-100 text-gray-600'
                  }`}
                >
                  <span>{localeFlags[locale]}</span>
                  <span className="hidden sm:inline">{localeNames[locale]}</span>
                  <span className="sm:hidden">{locale.toUpperCase()}</span>
                  {hasTranslation ? (
                    <div className="w-2 h-2 bg-green-500 rounded-full ml-auto" />
                  ) : (
                    <div className="w-2 h-2 bg-gray-400 rounded-full ml-auto" />
                  )}
                </div>
              );
            })}
          </div>
        </div>

        {/* Helper Text */}
        <div className="text-sm text-muted-foreground">
          <p>{t('translationNote')}</p>
        </div>
      </CardContent>
    </Card>
  );
}
